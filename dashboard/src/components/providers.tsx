"use client";

import { SessionProvider } from "next-auth/react";
import { ThemeProvider } from "next-themes";
import { ReactNode } from "react";
import { TanstackQueryProvider } from "./providers/query-provider";
import { HydrationBoundaryProvider } from "./providers/hydration-boundary";
import { TRPCProvider } from "./providers/trpc-provider";
import { NSFWRevealProvider } from "./hubs/NSFWRevealContext";
import type { Session } from "next-auth";
import { DehydratedState } from "@/lib/tanstack-query";

export function Providers({
  children,
  dehydratedState = null,
  session
}: {
  children: ReactNode;
  dehydratedState?: DehydratedState | null;
  session?: Session | null;
}) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="dark"
      enableSystem={false}
      forcedTheme="dark"
    >
      <SessionProvider
        session={session}
        refetchInterval={5 * 60} // Refetch session every 5 minutes instead of default 1 minute
        refetchOnWindowFocus={false} // Disable refetch on window focus
        refetchWhenOffline={false} // Disable refetch when offline
      >
        <TanstackQueryProvider>
          <HydrationBoundaryProvider state={dehydratedState}>
            <TRPCProvider>
              <NSFWRevealProvider>
                {children}
              </NSFWRevealProvider>
            </TRPCProvider>
          </HydrationBoundaryProvider>
        </TanstackQueryProvider>
      </SessionProvider>
    </ThemeProvider>
  );
}
