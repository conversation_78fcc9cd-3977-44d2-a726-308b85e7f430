'use client';

import { But<PERSON> } from '@/components/ui/button';
import { ArrowRight, HelpCircle, ChevronDown, Plus } from 'lucide-react';
import Link from 'next/link';
import { motion, AnimatePresence } from 'motion/react';
import { useState } from 'react';
import { cn } from '@/lib/utils';

// Custom Accordion Component
interface AccordionItemProps {
  readonly title: string;
  readonly content: React.ReactNode;
  readonly isOpen: boolean;
  readonly onToggle: () => void;
  readonly index: number;
}

function AccordionItem({ title, content, isOpen, onToggle, index }: AccordionItemProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      className="group"
    >
      <div
        className={cn(
          'relative bg-gradient-to-br from-gray-900 to-gray-950 backdrop-blur-sm',
          'border border-gray-800/50 hover:border-gray-700/70',
          'transition-all duration-300 overflow-hidden',
          'hover:shadow-xl hover:shadow-purple-500/10',
          isOpen ? 'rounded-[var(--radius)] rounded-b-none' : 'rounded-[var(--radius)]',
          'shadow-lg shadow-black/20',
        )}
      >
        {/* Enhanced background decoration */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 via-transparent to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

        {/* Subtle glass morphism overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/3 to-transparent pointer-events-none" />

        {/* Glow effect for open state */}
        {isOpen && (
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-blue-500/10 rounded-[var(--radius)] rounded-b-none" />
        )}

        <button
          onClick={onToggle}
          className={cn(
            'relative w-full px-6 py-6 lg:px-8 lg:py-7',
            'flex items-center justify-between',
            'text-left focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-2 focus:ring-offset-gray-900',
            'transition-all duration-300',
            'hover:bg-gradient-to-r hover:from-gray-800/20 hover:to-gray-700/20',
            'focus-visible:ring-2 focus-visible:ring-purple-400/50',
          )}
          aria-expanded={isOpen}
          aria-controls={`faq-content-${index}`}
          tabIndex={0}
        >
          <div className="flex items-center gap-5 flex-1">
            {/* Enhanced icon with improved design system compliance */}
            <div
              className={cn(
                'flex items-center justify-center w-11 h-11 rounded-[var(--radius-button)]',
                'bg-gradient-to-br from-purple-500/15 to-blue-500/15',
                'border border-purple-500/25 backdrop-blur-sm',
                'transition-all duration-300 group-hover:scale-105',
                'group-hover:shadow-lg group-hover:shadow-purple-500/20',
                isOpen &&
                  'bg-gradient-to-br from-purple-500/25 to-blue-500/25 border-purple-400/40 shadow-lg shadow-purple-500/25 scale-105',
              )}
            >
              <motion.div
                animate={{ rotate: isOpen ? 45 : 0 }}
                transition={{ duration: 0.3, ease: 'easeInOut' }}
              >
                <Plus
                  className={cn(
                    'w-5 h-5 transition-colors duration-300',
                    isOpen ? 'text-purple-300' : 'text-purple-400 group-hover:text-purple-300',
                  )}
                />
              </motion.div>
            </div>

            <h3 className="text-lg lg:text-xl font-semibold text-white group-hover:text-purple-100 transition-colors duration-300 leading-tight">
              {title}
            </h3>
          </div>

          {/* Enhanced chevron indicator */}
          <motion.div
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="ml-4 p-1"
          >
            <ChevronDown
              className={cn(
                'w-5 h-5 transition-colors duration-300',
                isOpen ? 'text-purple-300' : 'text-gray-400 group-hover:text-gray-300',
              )}
            />
          </motion.div>
        </button>
      </div>

      {/* Enhanced content with smooth animation */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            id={`faq-content-${index}`}
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{
              duration: 0.4,
              ease: [0.25, 0.46, 0.45, 0.94],
              opacity: { duration: 0.3 },
            }}
            className="overflow-hidden"
          >
            <div
              className={cn(
                'bg-gradient-to-br from-gray-800/85 to-gray-900/90 backdrop-blur-sm',
                'border-x border-b border-gray-800/50',
                'rounded-b-[var(--radius)]',
                'relative shadow-xl shadow-black/20',
              )}
            >
              {/* Enhanced content background decoration */}
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/3 via-transparent to-blue-500/3" />

              {/* Content area with improved padding */}
              <div className="relative p-6 lg:p-8">
                <div className="text-gray-300 leading-relaxed space-y-4 text-base lg:text-lg">
                  {content}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}

export function FaqSection() {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const toggleAccordion = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section
      className="relative overflow-hidden py-20 lg:py-32 bg-gray-900"
      id="faq"
    >
      {/* Enhanced background layers */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Primary radial gradient */}
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-gray-800/20 via-transparent to-transparent" />

        {/* Animated gradient orbs for depth */}
        <motion.div
          animate={{ opacity: [0.3, 0.6, 0.3] }}
          className="absolute top-0 left-1/4 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
opacity: [0.4, 0.7, 0.4],
          }}
          className="absolute bottom-0 right-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"
        />

        {/* Subtle grid pattern */}
        <div className="absolute inset-0 bg-grid-white bg-[size:60px_60px] opacity-[0.02] [mask-image:radial-gradient(ellipse_80%_80%_at_50%_50%,#000_20%,transparent_120%)]" />
      </div>

      <div className="container mx-auto px-4 relative">
        {/* Enhanced header section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16 lg:mb-20 relative z-10"
        >
          {/* Badge */}
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="inline-block mb-6"
          >
            <div className="flex items-center justify-center bg-gray-800/50 text-white px-6 py-3 rounded-[var(--radius-badge)] border border-gray-700/50 backdrop-blur-lg shadow-md hover:shadow-lg hover:shadow-purple-500/10 transition-all duration-300">
              <HelpCircle className="w-5 h-5 mr-3 text-purple-400" aria-hidden="true" />
              <span className="font-medium tracking-wide text-base">
                Frequently Asked Questions
              </span>
            </div>
          </motion.div>

          {/* Title */}
          <motion.h2
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight"
          >
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-white via-gray-100 to-white">
              Everything About
            </span>
            <span className="block mt-2 md:mt-3 bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-indigo-400 to-blue-400">
              InterChat
            </span>
          </motion.h2>

          {/* Description */}
          <motion.p
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="max-w-3xl mx-auto text-lg text-gray-300 leading-relaxed"
          >
            Everything you need to know about connecting your Discord communities with InterChat.
            Find answers to common questions about setup, features, and moderation.
          </motion.p>
        </motion.div>

        {/* Enhanced FAQ accordion container */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="group relative max-w-4xl mx-auto"
        >
          {/* Animated glow effect */}
          <motion.div
            animate={{
              opacity: [0, 0.3, 0],
              scale: [0.95, 1.05, 0.95],
            }}
            transition={{
              repeat: Infinity,
              duration: 4,
              ease: 'easeInOut',
            }}
            className="absolute inset-0 rounded-[var(--radius-modal)] bg-gradient-to-r from-purple-500/10 to-blue-500/10 blur-xl"
          />

          {/* FAQ items container */}
          <div className="relative space-y-4">
            {faqs.map((faq, index) => (
              <div
                key={faq.id}
                itemScope
                itemProp="mainEntity"
                itemType="https://schema.org/Question"
              >
                <AccordionItem
                  title={faq.title}
                  content={
                    <div itemScope itemProp="acceptedAnswer" itemType="https://schema.org/Answer">
                      <div itemProp="text">{faq.content}</div>
                    </div>
                  }
                  isOpen={openIndex === index}
                  onToggle={() => toggleAccordion(index)}
                  index={index}
                />
              </div>
            ))}
          </div>
        </motion.div>

        {/* Enhanced call-to-action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mt-16 lg:mt-20 text-center relative z-10"
        >
          <Button
            variant="ghost"
            className={cn(
              'group/button relative px-8 py-4 rounded-[var(--radius-button)]',
              'bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-xl',
              'border border-gray-600/50 hover:border-purple-500/50',
              'transition-all duration-300',
              'hover:shadow-lg hover:shadow-purple-500/20 hover:scale-105',
              'focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-2 focus:ring-offset-gray-900',
            )}
            asChild
          >
            <Link
              href="/docs"
              className="flex items-center justify-center gap-3"
              aria-label="View All InterChat Documentation"
            >
              <span className="font-medium bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-400 group-hover/button:from-purple-300 group-hover/button:to-blue-300 transition-all duration-300">
                View All InterChat Documentation
              </span>
              <div className="p-1.5 rounded-[var(--radius-button)] bg-gradient-to-r from-purple-500/20 to-blue-500/20 border border-purple-500/30 group-hover/button:from-purple-500/30 group-hover/button:to-blue-500/30 group-hover/button:border-purple-400/40 transition-all duration-300">
                <ArrowRight
                  className="w-4 h-4 text-purple-400 group-hover/button:text-purple-300 transition-all duration-300 transform group-hover/button:translate-x-1"
                  aria-hidden="true"
                />
              </div>
            </Link>
          </Button>
        </motion.div>
      </div>
    </section>
  );
}

const faqs = [
  {
    id: 'what-is-interchat',
    title: 'What is InterChat?',
    content: (
      <>
        InterChat is a Discord bot that makes multi-server messaging possible. It allows you to link
        a channel in your server to a &quot;hub&quot; (or group chat) where you can chat with other
        servers that have also linked their channels. Discover how InterChat can transform your
        Discord community.
      </>
    ),
  },
  {
    id: 'how-do-i-get-started',
    title: 'How do I get started with InterChat?',
    content: (
      <>
        To get started, invite the bot to your server and use the /setup command to link a channel
        to a hub. You can then start chatting with other servers that have also linked their
        channels. For more information, check out the{' '}
        <Link
          href="/docs"
          className="text-purple-400 hover:text-purple-300 font-medium underline underline-offset-2 transition-colors duration-200"
        >
          documentation
        </Link>
        . Explore the power of InterChat and unlock the potential of cross-server communication.
      </>
    ),
  },
  {
    id: 'what-is-a-hub',
    title: 'What is an InterChat hub?',
    content: (
      <>
        A hub is an InterChat feature that acts similar to a traditional &quot;group chat&quot;.
        Servers can link their channels to it and talk. When you link a channel to a hub, you can
        chat with other servers that have also linked their channels to the same hub. Hubs can be
        public or private, and you can create your own hub or join an existing one. Find awesome
        community made hubs using the{' '}
        <Link
          href="/hubs"
          className="text-purple-400 hover:text-purple-300 font-medium underline underline-offset-2 transition-colors duration-200"
        >
          Hub Browser
        </Link>
        ! Join an InterChat hub today and connect with like-minded communities.
      </>
    ),
  },
  {
    id: 'is-interchat-free-to-use',
    title: 'Is InterChat free to use?',
    content: (
      <>
        InterChat is a completely free and open-source project that is developed by the community
        for the community. If you would like to support the project, you can do so by contributing
        to the codebase, reporting bugs, or suggesting new features. You can also support the
        project by becoming a sponsor on GitHub or donating to the project on{' '}
        <Link
          href="/donate"
          className="text-purple-700 dark:text-purple-400 hover:text-purple-500 dark:hover:text-purple-300 font-medium"
        >
          Ko-Fi
        </Link>{' '}
        to help us continue to provide a free and open-source solution for Discord community
        connectivity.
      </>
    ),
  },
  {
    id: 'advanced-features',
    title: 'How do I learn more about InterChat?',
    content: (
      <>
        You can learn more about advanced features by checking out the{' '}
        <Link
          href="/docs"
          className="text-purple-400 hover:text-purple-300 font-medium underline underline-offset-2 transition-colors duration-200"
        >
          documentation
        </Link>
        . We worked hard to make sure the commands are easy to understand and act as a guide to help
        you get started. Unlock the full potential of InterChat with our comprehensive
        documentation.
      </>
    ),
  },
];
