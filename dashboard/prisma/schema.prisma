generator client {
  provider        = "prisma-client-js"
  output          = "../src/lib/generated/prisma/client"
  previewFeatures = ["postgresqlExtensions", "relationJoins"]
  moduleFormat    = "esm"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([provider, providerAccountId])
}

model Achievement {
  id               String                    @id @default(cuid())
  name             String
  description      String
  badgeEmoji       String
  badgeUrl         String
  createdAt        DateTime                  @default(now())
  updatedAt        DateTime                  @default(now())
  threshold        Int
  secret           Boolean                   @default(false)
  userAchievements UserAchievement[]
  userProgress     UserAchievementProgress[]
}

model Announcement {
  id           String   @id @default(cuid())
  title        String
  content      String
  imageUrl     String?
  createdAt    DateTime @default(now())
  thumbnailUrl String?
}

model AntiSwearPattern {
  id      String        @id @default(cuid())
  ruleId  String
  pattern String
  isRegex Boolean       @default(false)
  rule    AntiSwearRule @relation(fields: [ruleId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([ruleId])
}

model AntiSwearRule {
  id                       String             @id @default(cuid())
  hubId                    String
  name                     String
  createdBy                String
  muteDurationMinutes      Int?
  blacklistDurationMinutes Int?
  createdAt                DateTime           @default(now())
  updatedAt                DateTime           @default(now())
  actions                  BlockWordAction[]  @default([])
  logViolations            Boolean            @default(false)
  patterns                 AntiSwearPattern[]
  creator                  User               @relation(fields: [createdBy], references: [id], onDelete: NoAction, onUpdate: NoAction)
  hub                      Hub                @relation(fields: [hubId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([hubId, name])
  @@index([hubId])
}

model Appeal {
  infractionId String
  userId       String
  reason       String
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @default(now())
  id           String       @id @default(cuid())
  status       AppealStatus @default(PENDING)
  infraction   Infraction   @relation(fields: [infractionId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user         User         @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([infractionId])
  @@index([status])
  @@index([userId])
  @@index([createdAt])
}

model BlockWord {
  id        String            @id @default(cuid())
  hubId     String
  name      String
  createdBy String
  createdAt DateTime          @default(now())
  updatedAt DateTime          @default(now())
  words     String
  actions   BlockWordAction[]
  creator   User              @relation(fields: [createdBy], references: [id], onDelete: NoAction, onUpdate: NoAction)
  hub       Hub               @relation(fields: [hubId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([hubId, name])
  @@index([hubId])
}

model Broadcast {
  id        String   @id @default(cuid())
  messageId String
  channelId String
  createdAt DateTime @default(now())
  message   Message  @relation(fields: [messageId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([channelId])
  @@index([messageId])
  @@index([createdAt(sort: Desc)])
  @@index([id, messageId, channelId, createdAt])
  @@index([messageId, channelId])
}

model Call {
  id           String            @id @default(cuid())
  initiatorId  String
  startTime    DateTime          @default(now())
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @default(now())
  endTime      DateTime?
  status       CallStatus        @default(QUEUED)
  messages     CallMessage[]
  participants CallParticipant[]
  ratings      CallRating[]

  @@index([initiatorId])
  @@index([startTime])
  @@index([status])
}

model CallMessage {
  id             String   @id
  callId         String
  authorId       String
  authorUsername String
  content        String
  attachmentUrl  String?
  timestamp      DateTime @default(now())
  call           Call     @relation(fields: [callId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([authorId])
  @@index([callId])
  @@index([timestamp])
}

model CallParticipant {
  id           String                @id @default(cuid())
  callId       String
  leftAt       DateTime?
  joinedAt     DateTime              @default(now())
  channelId    String
  guildId      String
  webhookUrl   String
  messageCount Int                   @default(0)
  call         Call                  @relation(fields: [callId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  users        CallParticipantUser[]

  @@unique([callId, channelId])
  @@index([callId])
  @@index([channelId])
  @@index([guildId])
}

model CallParticipantUser {
  id            String          @id @default(cuid())
  leftAt        DateTime
  participantId String
  userId        String
  joinedAt      DateTime        @default(now())
  participant   CallParticipant @relation(fields: [participantId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([participantId, userId])
  @@index([participantId])
  @@index([userId])
}

model CallRating {
  id        String           @id @default(cuid())
  callId    String
  raterId   String
  targetId  String
  rating    CallRatingStatus
  timestamp DateTime         @default(now())
  call      Call             @relation(fields: [callId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  rater     User             @relation("CallRating_raterIdToUser", fields: [raterId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  target    User             @relation("CallRating_targetIdToUser", fields: [targetId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([callId, raterId, targetId])
  @@index([callId])
  @@index([raterId])
  @@index([targetId])
}

model Connection {
  id         String     @id @default(cuid())
  channelId  String     @unique
  invite     String?
  webhookURL String
  serverId   String
  hubId      String
  createdAt  DateTime   @default(now())
  lastActive DateTime   @default(now())
  parentId   String?
  connected  Boolean    @default(true)
  hub        Hub        @relation(fields: [hubId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  server     ServerData @relation(fields: [serverId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([channelId, serverId])
  @@unique([hubId, serverId])
  @@index([channelId, connected])
  @@index([hubId, channelId])
  @@index([hubId, connected])
  @@index([lastActive])
  @@index([hubId])
  @@index([serverId])
}

model Donation {
  id                String                  @id @default(cuid())
  kofiTransactionId String                  @unique
  messageId         String?                 @unique
  amount            Float
  currency          String
  fromName          String
  message           String?
  email             String
  kofiTimestamp     DateTime
  kofiUrl           String
  donationTierId    String?
  discordUserId     String?
  createdAt         DateTime                @default(now())
  updatedAt         DateTime                @default(now())
  isPublic          Boolean                 @default(true)
  processed         Boolean                 @default(false)
  discordUser       User?                   @relation(fields: [discordUserId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  donationTier      DonationTierDefinition? @relation(fields: [donationTierId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([createdAt])
  @@index([discordUserId])
  @@index([kofiTransactionId])
}

model Hub {
  id                  String               @id @default(cuid())
  name                String               @unique
  description         String
  ownerId             String
  iconUrl             String
  shortDescription    String?              @db.VarChar(100)
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @default(now())
  lastActive          DateTime             @default(now())
  lastNameChange      DateTime?            @default(now())
  bannerUrl           String?
  welcomeMessage      String?
  language            String?
  region              String?
  settings            Int                  @default(0)
  appealCooldownHours Int                  @default(168)
  weeklyMessageCount  Int                  @default(0)
  private             Boolean              @default(true)
  locked              Boolean              @default(false)
  nsfw                Boolean              @default(false)
  verified            Boolean              @default(false)
  partnered           Boolean              @default(false)
  featured            Boolean              @default(false)
  rules               String[]
  activityLevel       HubActivityLevel     @default(LOW)
  antiSwearRules      AntiSwearRule[]
  blockWords          BlockWord[]
  connections         Connection[]
  owner               User                 @relation(fields: [ownerId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  activityMetrics     HubActivityMetrics?
  invites             HubInvite[]
  logConfig           HubLogConfig?
  moderators          HubModerator[]
  reviews             HubReview[]
  rulesAcceptances    HubRulesAcceptance[]
  upvotes             HubUpvote[]
  infractions         Infraction[]
  leaderboardEntries  LeaderboardEntry[]
  messages            Message[]
  reports             Report[]
  tags                Tag[]

  @@index([ownerId])
  @@index([activityLevel])
  @@index([language])
  @@index([nsfw])
  @@index([verified, featured, private])
  @@index([weeklyMessageCount])
}

model HubActivityMetrics {
  id                    String   @id @default(cuid())
  hubId                 String   @unique
  lastUpdated           DateTime @default(now())
  createdAt             DateTime @default(now())
  messagesLast24h       Int      @default(0)
  activeUsersLast24h    Int      @default(0)
  newConnectionsLast24h Int      @default(0)
  messagesLast7d        Int      @default(0)
  activeUsersLast7d     Int      @default(0)
  newConnectionsLast7d  Int      @default(0)
  memberGrowthRate      Float    @default(0.0)
  engagementRate        Float    @default(0.0)
  hub                   Hub      @relation(fields: [hubId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([hubId])
  @@index([lastUpdated])
}

model HubInvite {
  code      String    @id @unique @default(nanoid())
  hubId     String
  expires   DateTime?
  createdAt DateTime  @default(now())
  maxUses   Int       @default(0)
  uses      Int       @default(0)
  hub       Hub       @relation(fields: [hubId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([hubId])
}

model HubLogConfig {
  id                         String  @id @default(cuid())
  hubId                      String  @unique
  modLogsChannelId           String?
  modLogsRoleId              String?
  joinLeavesChannelId        String?
  joinLeavesRoleId           String?
  appealsChannelId           String?
  appealsRoleId              String?
  reportsChannelId           String?
  reportsRoleId              String?
  networkAlertsChannelId     String?
  networkAlertsRoleId        String?
  messageModerationChannelId String?
  messageModerationRoleId    String?
  hub                        Hub     @relation(fields: [hubId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([hubId])
}

model HubModerator {
  id     String @id @default(cuid())
  hubId  String
  userId String
  role   Role
  hub    Hub    @relation(fields: [hubId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  user   User   @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([hubId, userId])
  @@index([userId])
}

model HubReview {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())
  rating    Int
  text      String
  hubId     String
  userId    String
  hub       Hub      @relation(fields: [hubId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  user      User     @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([hubId, userId])
  @@index([userId])
}

model HubRulesAcceptance {
  id         String   @id @default(cuid())
  userId     String
  hubId      String
  acceptedAt DateTime @default(now())
  hub        Hub      @relation(fields: [hubId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  user       User     @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([userId, hubId])
  @@index([hubId, userId])
}

model HubUpvote {
  id        String   @id @default(cuid())
  userId    String
  createdAt DateTime @default(now())
  hubId     String
  hub       Hub      @relation(fields: [hubId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  user      User     @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([hubId, userId])
  @@index([userId])
}

model Infraction {
  id          String           @id @default(cuid())
  hubId       String
  moderatorId String
  reason      String
  expiresAt   DateTime?
  userId      String?
  serverId    String?
  serverName  String?
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @default(now())
  type        InfractionType   @default(BAN)
  status      InfractionStatus @default(ACTIVE)
  notified    Boolean          @default(false)
  appeals     Appeal[]
  hub         Hub              @relation(fields: [hubId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  moderator   User             @relation("Infraction_moderatorIdToUser", fields: [moderatorId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  server      ServerData?      @relation(fields: [serverId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user        User?            @relation("Infraction_userIdToUser", fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([status, hubId])
  @@index([expiresAt])
  @@index([serverId])
  @@index([type])
  @@index([userId])
}

model Message {
  id                String      @id
  hubId             String
  content           String
  imageUrl          String?
  channelId         String
  guildId           String
  authorId          String
  reactions         Json?       @db.Json
  referredMessageId String?
  createdAt         DateTime    @default(now())
  broadcasts        Broadcast[]
  hub               Hub         @relation(fields: [hubId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  referredTo        Message?    @relation("MessageToMessage", fields: [referredMessageId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  referredBy        Message[]   @relation("MessageToMessage")
  reports           Report[]

  @@index([authorId])
  @@index([createdAt(sort: Desc)])
  @@index([hubId])
  @@index([referredMessageId])
  @@index([guildId, authorId])
  @@index([guildId, hubId])
  @@index([guildId])
  @@index([hubId, createdAt(sort: Desc)])
}

model PendingClaim {
  id                String    @id @default(cuid())
  kofiTransactionId String    @unique
  email             String
  tierName          String?
  amount            Float
  kofiUrl           String
  currency          String
  fromName          String
  expiresAt         DateTime
  claimedBy         String?
  claimedAt         DateTime?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @default(now())
  claimed           Boolean   @default(false)

  @@index([claimed])
  @@index([email])
  @@index([expiresAt])
  @@index([kofiTransactionId])
}

model Report {
  id               String       @id @default(cuid())
  hubId            String
  reporterId       String
  reportedUserId   String
  reportedServerId String
  messageId        String?
  handledBy        String?
  handledAt        DateTime?
  reason           String
  createdAt        DateTime     @default(now())
  updatedAt        DateTime     @default(now())
  status           ReportStatus @default(PENDING)
  handler          User?        @relation("Report_handledByToUser", fields: [handledBy], references: [id], onDelete: NoAction, onUpdate: NoAction)
  hub              Hub          @relation(fields: [hubId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  message          Message?     @relation(fields: [messageId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  reportedUser     User         @relation("Report_reportedUserIdToUser", fields: [reportedUserId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  reporter         User         @relation("Report_reporterIdToUser", fields: [reporterId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([createdAt])
  @@index([hubId])
  @@index([reportedUserId])
  @@index([reporterId])
  @@index([status])
  @@index([handledBy])
  @@index([messageId])
}

model ReputationLog {
  id         String   @id @default(cuid())
  giverId    String
  receiverId String
  reason     String
  timestamp  DateTime @default(now())
  automatic  Boolean  @default(false)
  receiver   User     @relation(fields: [receiverId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([giverId])
  @@index([receiverId])
}

model ServerData {
  id                 String             @id
  name               String?
  createdAt          DateTime           @default(now())
  updatedAt          DateTime           @default(now())
  lastMessageAt      DateTime           @default(now())
  inviteCode         String?
  messageCount       Int                @default(0)
  premiumStatus      Boolean            @default(false)
  iconUrl            String?
  connections        Connection[]
  infractions        Infraction[]
  leaderboardEntries LeaderboardEntry[]
  serverBlacklists   ServerBlacklist[]
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model Tag {
  id          String   @id @default(cuid())
  name        String   @unique
  category    String?
  description String?
  color       String?
  createdAt   DateTime @default(now())
  isOfficial  Boolean  @default(false)
  usageCount  Int      @default(0)
  hubs        Hub[]

  @@index([category])
  @@index([usageCount])
}

model User {
  id                     String                    @id
  name                   String?
  image                  String?
  lastMessageAt          DateTime?                 @default(now())
  inboxLastReadDate      DateTime?                 @default(now())
  createdAt              DateTime?                 @default(now())
  updatedAt              DateTime?                 @default(now())
  donationTierId         String?
  tagId                  String?
  activityLevel          HubActivityLevel?
  donationExpiresAt      DateTime?
  lastHubJoinAt          DateTime?
  email                  String?
  emailVerified          DateTime?
  donationEmail          String?
  showBadges             Boolean                   @default(true)
  mentionOnReply         Boolean                   @default(true)
  showNsfwHubs           Boolean                   @default(false)
  voteCount              Int                       @default(0)
  reputation             Int                       @default(0)
  messageCount           Int                       @default(0)
  hubJoinCount           Int                       @default(0)
  hubEngagementScore     Float                     @default(0.0)
  locale                 String?
  lastVoted              DateTime?
  badges                 Badges[]                  @default([])
  preferredLanguages     String[]                  @default([])
  accounts               Account[]
  antiSwearRulesCreated  AntiSwearRule[]
  appeals                Appeal[]
  blacklists             Blacklist[]               @relation("Blacklist_userIdToUser")
  issuedBlacklists       Blacklist[]               @relation("Blacklist_moderatorIdToUser")
  blockWordsCreated      BlockWord[]
  ratingsMade            CallRating[]              @relation("CallRating_raterIdToUser")
  ratingsReceived        CallRating[]              @relation("CallRating_targetIdToUser")
  donations              Donation[]
  ownedHubs              Hub[]
  modPositions           HubModerator[]
  reviews                HubReview[]
  rulesAcceptances       HubRulesAcceptance[]
  upvotedHubs            HubUpvote[]
  issuedInfractions      Infraction[]              @relation("Infraction_moderatorIdToUser")
  infractions            Infraction[]              @relation("Infraction_userIdToUser")
  leaderboardEntries     LeaderboardEntry[]
  reportsHandled         Report[]                  @relation("Report_handledByToUser")
  reportsReceived        Report[]                  @relation("Report_reportedUserIdToUser")
  reportsSubmitted       Report[]                  @relation("Report_reporterIdToUser")
  reputationLog          ReputationLog[]
  issuedServerBlacklists ServerBlacklist[]
  sessions               Session[]
  donationTier           DonationTierDefinition?   @relation(fields: [donationTierId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  achievements           UserAchievement[]
  achievementProgress    UserAchievementProgress[]

  @@index([createdAt])
  @@index([email])
  @@index([lastVoted])
  @@index([locale])
  @@index([reputation])
  @@index([voteCount])
}

model UserAchievement {
  id            String      @id @default(cuid())
  userId        String
  achievementId String
  unlockedAt    DateTime    @default(now())
  achievement   Achievement @relation(fields: [achievementId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  user          User        @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([userId, achievementId])
  @@index([achievementId])
  @@index([userId])
}

model UserAchievementProgress {
  userId        String
  achievementId String
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @default(now())
  currentValue  Int         @default(0)
  achievement   Achievement @relation(fields: [achievementId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  user          User        @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@id([userId, achievementId])
}

model Blacklist {
  id          String        @id
  userId      String
  moderatorId String
  reason      String
  expiresAt   DateTime?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime?     @default(now())
  type        BlacklistType @default(PERMANENT)
  moderator   User          @relation("Blacklist_moderatorIdToUser", fields: [moderatorId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user        User          @relation("Blacklist_userIdToUser", fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([createdAt])
  @@index([expiresAt])
  @@index([userId, expiresAt])
  @@index([userId])
}

model DonationTierDefinition {
  id          String     @id
  name        String     @unique
  description String
  price       Float
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @default(now())
  donations   Donation[]
  users       User[]
}

model LeaderboardEntry {
  id             String            @id
  userId         String
  hubId          String
  serverId       String
  period         LeaderboardPeriod
  type           LeaderboardType
  lastActivityAt DateTime          @default(now())
  createdAt      DateTime          @default(now())
  updatedAt      DateTime          @default(now())
  messageCount   Int               @default(0)
  score          Int               @default(0)
  rank           Int               @default(0)
  hub            Hub               @relation(fields: [hubId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  server         ServerData        @relation(fields: [serverId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  user           User              @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([hubId, type, period])
  @@index([lastActivityAt])
  @@index([serverId, type, period])
  @@index([type, period, score])
  @@index([userId, type, period])
}

model ServerBlacklist {
  id          String        @id
  serverId    String
  moderatorId String
  reason      String
  duration    Int?
  expiresAt   DateTime?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @default(now())
  type        BlacklistType @default(PERMANENT)
  moderator   User          @relation(fields: [moderatorId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  server      ServerData    @relation(fields: [serverId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([createdAt])
  @@index([expiresAt])
  @@index([serverId, expiresAt])
  @@index([serverId])
}

enum AppealStatus {
  PENDING
  ACCEPTED
  REJECTED
}

enum BlockWordAction {
  BLOCK_MESSAGE
  SEND_ALERT
  WARN
  MUTE
  BAN
  BLACKLIST
}

enum CallRatingStatus {
  LIKE
  DISLIKE
}

enum CallStatus {
  QUEUED
  ACTIVE
  ENDED
}

enum HubActivityLevel {
  LOW
  MEDIUM
  HIGH
}

enum InfractionStatus {
  ACTIVE
  REVOKED
  APPEALED
}

enum InfractionType {
  BAN
  BLACKLIST
  WARNING
}

enum ReportStatus {
  PENDING
  RESOLVED
  IGNORED
}

enum Role {
  MODERATOR
  MANAGER
}

enum Badges {
  VOTER
  SUPPORTER
  TRANSLATOR
  DEVELOPER
  STAFF
  BETA_TESTER
}

enum BlacklistType {
  PERMANENT
  TEMPORARY
}

enum LeaderboardPeriod {
  DAILY
  WEEKLY
  MONTHLY
  ALL_TIME
}

enum LeaderboardType {
  USER
  SERVER
  HUB
}
