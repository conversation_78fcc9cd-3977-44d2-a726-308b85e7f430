import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { <PERSON>R<PERSON>, Zap } from "lucide-react";

export function V5CTA() {
  return (
    <section className="relative py-16 md:py-24 bg-gradient-to-b from-gray-950 to-gray-900">
      <div className="container mx-auto px-4 text-center">
        <div className="inline-flex items-center gap-2 rounded-full border border-gray-700/60 bg-gray-800/40 px-4 py-1.5 text-sm text-gray-300 shadow-md mb-6">
          <Zap className="h-4 w-4 text-emerald-400" />
          Faster, cleaner, and easier to use
        </div>
        <h3 className="text-3xl md:text-5xl font-bold text-white tracking-tight">
          Get started with InterChat v5
        </h3>
        <p className="mt-3 text-gray-400 max-w-2xl mx-auto">
          Connect your Discord servers in minutes. Explore public hubs or build your own community network.
        </p>
        <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild size="lg" className="h-12 px-8">
            <Link href="/hubs">Discover Hubs</Link>
          </Button>
          <Button asChild size="lg" variant="outline" className="h-12 px-8">
            <Link href="/docs/getting-started" className="flex items-center">
              Learn how it works
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}

