"use client";

import { motion } from "motion/react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, Command, Gauge, LayoutDashboard, MessageSquare } from "lucide-react";
import Link from "next/link";

const features = [
  {
    title: "Rewritten Commands",
    description:
      "Cleaner, more reliable slash commands with improved validation and feedback.",
    icon: Command,
    color: "from-purple-500/20 to-indigo-500/20",
  },
  {
    title: "Improved Performance",
    description:
      "Optimized pipeline for faster broadcasts, lower latency, and smoother moderation.",
    icon: Gauge,
    color: "from-emerald-500/20 to-green-500/20",
  },
  {
    title: "Enhanced Command UIs",
    description:
      "Modern UI patterns and micro-interactions for a delightful command experience.",
    icon: MessageSquare,
    color: "from-blue-500/20 to-cyan-500/20",
  },
  {
    title: "Redesigned Dashboard",
    description:
      "Polished, cohesive design system with responsive layouts and smooth transitions.",
    icon: LayoutDashboard,
    color: "from-pink-500/20 to-rose-500/20",
  },
];

export function V5FeatureShowcase() {
  return (
    <section className="relative overflow-hidden py-16 md:py-24 bg-gradient-to-b from-gray-900 to-gray-950 border-b border-gray-800/50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-5xl font-bold text-white tracking-tight">
            What’s new in <span className="text-primary">v5</span>
          </h2>
          <p className="mt-3 text-gray-400 max-w-2xl mx-auto">
            A modern foundation across the stack, designed for reliability and scale.
          </p>
        </div>

        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {features.map((f, i) => (
            <motion.div
              key={f.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ duration: 0.5, delay: i * 0.05 }}
            >
              <Card className="relative overflow-hidden rounded-[var(--radius)] border-gray-700/60 bg-gray-800/40 backdrop-blur-xl">
                <div className={`absolute inset-0 bg-gradient-to-br ${f.color} opacity-40`} />
                <div className="relative p-6 min-h-40 flex flex-col gap-3">
                  <div className="inline-flex items-center justify-center size-10 rounded-[var(--radius)] bg-gray-900/70 border border-gray-700/60">
                    <f.icon className="h-5 w-5 text-primary" />
                  </div>
                  <h3 className="text-lg font-semibold text-white">{f.title}</h3>
                  <p className="text-sm text-gray-300">{f.description}</p>
                </div>
              </Card>
            </motion.div>
          ))}
        </div>

        <div className="mt-10 flex justify-center">
          <Button asChild size="lg" variant="outline" className="h-12">
            <Link href="/docs/getting-started" className="flex items-center">
              Read the Getting Started Guide
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}

