---
title: Complete Guides
description: Master every aspect of InterChat with our comprehensive tutorials and best practices guides.
icon: BookOpen
---


Whether you're just getting started or looking to master advanced features, our guides provide step-by-step instructions, best practices, and real-world examples to help you succeed.

## Getting Started

<Cards>
  <Card title="Creating Your First Hub" href="/docs/guides/creating-hubs">
    Learn how to create and configure a hub that attracts and retains community members
  </Card>
  <Card title="Connecting Servers" href="/docs/guides/connections">
    Master the art of connecting channels and managing cross-server communication
  </Card>
  <Card title="Essential Commands" href="/docs/guides/commands">
    Quick reference for the most important commands you'll use daily
  </Card>
</Cards>

## Hub Management

<Cards>
  <Card title="Hub Administration" href="/docs/guides/hub-management">
    Complete guide to managing settings, rules, and community features
  </Card>
  <Card title="Making Hubs Public" href="/docs/guides/hub-visibility">
    Requirements and best practices for publishing your hub to the directory
  </Card>
  <Card title="Invitation Management" href="/docs/guides/invitations">
    Create and manage private hub invitations effectively
  </Card>
</Cards>

## Community Safety

<Cards>
  <Card title="Moderation Essentials" href="/docs/guides/moderation">
    Keep your community safe with comprehensive moderation tools
  </Card>
  <Card title="Content Filtering" href="/docs/guides/content-filtering">
    Set up automated content filtering and anti-spam protection
  </Card>
  <Card title="Building a Mod Team" href="/docs/guides/moderation-team">
    Recruit, train, and manage an effective moderation team
  </Card>
</Cards>

## Advanced Features

<Cards>
  <Card title="Activity Logging" href="/docs/guides/logging">
    Monitor your hub with comprehensive activity logging and alerts
  </Card>
  <Card title="Custom Announcements" href="/docs/guides/announcements">
    Communicate effectively with hub-wide announcements
  </Card>
  <Card title="Welcome Systems" href="/docs/guides/welcome-messages">
    Create engaging welcome experiences for new servers
  </Card>
  <Card title="Calls & Achievements" href="/docs/guides/calls-achievements">
    Explore calls, leaderboards, and the achievement system
  </Card>
</Cards>

## Technical Guides

<Cards>
  <Card title="Self-Hosting Setup" href="/docs/guides/self-hosting">
    Run your own InterChat instance with complete control
  </Card>
  <Card title="API Integration" href="/docs/guides/api">
    Integrate InterChat with your own applications and services
  </Card>
  <Card title="Troubleshooting" href="/docs/guides/troubleshooting">
    Solve common issues and optimize your InterChat experience
  </Card>
</Cards>

## Best Practices

<Callout type="info">
  **Pro Tips for Success:**

  - **Start Small:** Begin with a focused community before expanding
  - **Set Clear Rules:** Establish expectations early and enforce consistently
  - **Engage Actively:** Participate in your own hub to encourage others
  - **Monitor Regularly:** Use logging to stay aware of community health
  - **Listen to Feedback:** Your community members are your best advisors
</Callout>

## Learning Path Recommendations

### For New Users
1. [Quick Start Guide](/docs/getting-started)
2. [Essential Commands](/docs/guides/commands)
3. [Connecting Servers](/docs/guides/connections)

### For Server Administrators
1. [Creating Your First Hub](/docs/guides/creating-hubs)
2. [Hub Administration](/docs/guides/hub-management)
3. [Moderation Essentials](/docs/guides/moderation)
4. [Activity Logging](/docs/guides/logging)

### For Community Builders
1. [Making Hubs Public](/docs/guides/hub-visibility)
2. [Building a Mod Team](/docs/guides/moderation-team)
3. [Custom Announcements](/docs/guides/announcements)
4. [Welcome Systems](/docs/guides/welcome-messages)

### For Technical Users
1. [Self-Hosting Setup](/docs/guides/self-hosting)
2. [API Integration](/docs/guides/api)
3. [Troubleshooting](/docs/guides/troubleshooting)

---

**Can't find what you're looking for?** Check our [FAQ](/docs/faq) or join our [support community](https://discord.gg/cgYgC6YZyX) for personalized help.
