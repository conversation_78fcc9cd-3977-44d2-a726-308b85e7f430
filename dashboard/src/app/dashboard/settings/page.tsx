import { auth } from '@/auth';
import { UserSettingsForm } from '@/components/dashboard/settings/user-settings-form';
import { KofiSection } from '@/components/KofiSection';
import { db } from '@/lib/prisma';
import { Metadata } from 'next';
import Image from 'next/image';
import { redirect } from 'next/navigation';
import { User, Settings, CreditCard, Mail } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Profile | InterChat Dashboard',
  description: 'Manage your InterChat settings and preferences',
};

export default async function SettingsPage() {
  const session = await auth();

  if (!session?.user) {
    redirect('/login?callbackUrl=/dashboard/settings');
  }

  // Fetch user data from database to get current settings
  const user = await db.user.findUnique({
    where: { id: session.user.id },
    select: {
      mentionOnReply: true,
      locale: true,
      showNsfwHubs: true,
      email: true,
      donationEmail: true,
    },
  });

  // Fetch Ko-fi donations and pending claims server-side
  const recentDonations = await db.donation.findMany({
    where: {
      discordUserId: session.user.id,
      processed: true,
    },
    orderBy: { kofiTimestamp: 'desc' },
    take: 10,
    select: {
      id: true,
      amount: true,
      currency: true,
      fromName: true,
      kofiTimestamp: true,
      processed: true,
    },
  });

  if (!user?.email && !user?.donationEmail) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-gradient-to-br from-amber-950/30 to-amber-900/20 border border-amber-500/20 rounded-xl p-6 text-center">
          <Mail className="h-12 w-12 text-amber-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-white mb-2">Email Required</h1>
          <p className="text-amber-200 mb-4">
            You need to link your email to access all settings and claim Ko-fi donations.
          </p>
          <a 
            href="/dashboard/settings/donation-email" 
            className="inline-flex items-center gap-2 bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200"
          >
            <Mail className="h-4 w-4" />
            Set Donation Email
          </a>
        </div>
      </div>
    );
  }

  const pendingClaims = await db.pendingClaim.findMany({
    where: {
      OR: [{ email: user?.email ?? '' }, { email: (user.donationEmail || user?.email)! }],
      claimed: false,
      expiresAt: { gt: new Date() },
    },
    orderBy: { createdAt: 'desc' },
    select: {
      id: true,
      tierName: true,
      amount: true,
      currency: true,
      fromName: true,
      expiresAt: true,
      createdAt: true,
    },
  });

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Account Settings</h1>
        <p className="text-gray-400">Manage your profile, preferences, and donations</p>
      </div>

      {/* Quick Stats/Info Bar */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <div className="bg-gradient-to-br from-gray-900/90 to-gray-950/90 backdrop-blur-sm border border-gray-700/50 rounded-xl p-4 text-center">
          <User className="h-6 w-6 text-purple-400 mx-auto mb-2" />
          <p className="text-gray-400 text-sm">Profile Status</p>
          <p className="text-white font-medium">Active</p>
        </div>
        <div className="bg-gradient-to-br from-gray-900/90 to-gray-950/90 backdrop-blur-sm border border-gray-700/50 rounded-xl p-4 text-center">
          <CreditCard className="h-6 w-6 text-emerald-400 mx-auto mb-2" />
          <p className="text-gray-400 text-sm">Donations</p>
          <p className="text-white font-medium">{recentDonations.length} processed</p>
        </div>
        <div className="bg-gradient-to-br from-gray-900/90 to-gray-950/90 backdrop-blur-sm border border-gray-700/50 rounded-xl p-4 text-center">
          <Settings className="h-6 w-6 text-blue-400 mx-auto mb-2" />
          <p className="text-gray-400 text-sm">Pending Claims</p>
          <p className="text-white font-medium">{pendingClaims.length} available</p>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        
        {/* Profile Section - Left Column */}
        <div className="space-y-6">
          <div className="bg-gradient-to-br from-gray-900/90 to-gray-950/90 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6">
            <div className="flex items-center gap-2 mb-4">
              <User className="h-5 w-5 text-purple-400" />
              <h2 className="text-xl font-semibold text-white">Profile</h2>
            </div>
            
            <div className="flex items-center gap-4 mb-4">
              <div className="relative">
                <Image
                  src={session.user.image || 'https://api.dicebear.com/7.x/shapes/svg?seed=user'}
                  alt={session.user.name || 'User'}
                  width={64}
                  height={64}
                  className="rounded-full border-2 border-purple-500/30"
                />
                <div className="absolute -bottom-1 -right-1 h-4 w-4 rounded-full bg-emerald-500 border-2 border-gray-900"></div>
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-medium text-white">{session.user.name}</h3>
                <p className="text-xs text-gray-400 font-mono bg-gray-800/50 px-2 py-1 rounded mt-1 inline-block">
                  ID: {session.user.id}
                </p>
              </div>
            </div>
            
            <div className="bg-blue-950/20 border border-blue-500/20 rounded-lg p-3">
              <p className="text-sm text-blue-200">
                <strong>Note:</strong> Profile info is synced from Discord. Update your avatar and username there.
              </p>
            </div>
          </div>

          {/* Account Preferences */}
          <div className="bg-gradient-to-br from-gray-900/90 to-gray-950/90 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6">
            <div className="flex items-center gap-2 mb-4">
              <Settings className="h-5 w-5 text-indigo-400" />
              <h2 className="text-xl font-semibold text-white">Preferences</h2>
            </div>
            
            <UserSettingsForm
              userId={session.user.id}
              initialMentionOnReply={user?.mentionOnReply ?? true}
              initialLocale={user?.locale ?? null}
              initialShowNsfwHubs={user?.showNsfwHubs ?? false}
            />
          </div>
        </div>

        {/* Ko-fi Section - Right Column */}
        <div className="space-y-6">
          <KofiSection pendingClaims={pendingClaims} recentDonations={recentDonations} />
        </div>
      </div>

      {/* Quick Actions Footer */}
      <div className="bg-gradient-to-r from-gray-900/50 to-gray-800/50 border border-gray-700/30 rounded-xl p-4">
        <div className="flex flex-wrap items-center justify-center gap-4">
          <a 
            href="/dashboard/settings/donation-email" 
            className="inline-flex items-center gap-2 bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 text-gray-300 hover:text-white px-4 py-2 rounded-lg text-sm transition-all duration-200"
          >
            <Mail className="h-4 w-4" />
            Update Email
          </a>
          <button className="inline-flex items-center gap-2 bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 text-gray-300 hover:text-white px-4 py-2 rounded-lg text-sm transition-all duration-200">
            <Settings className="h-4 w-4" />
            Advanced Settings
          </button>
        </div>
      </div>
    </div>
  );
}