---
title: Quick Start Guide
description: Get up and running with InterChat in under 5 minutes. Connect your first servers and start cross-server communication today.
icon: Rocket
---

# Get Started in 5 Minutes

Welcome! This guide will have you connecting Discord servers and chatting across communities in no time. Let's dive in!

## What You'll Learn
- How to add InterChat to your Discord server
- How to create or join your first hub
- How to connect a channel for cross-server chat
- Essential commands to get you started

<Callout type="info">
  **First time with Discord bots?** Don't worry! We'll walk you through everything step by step.
</Callout>

## Step 1: Add InterChat to Your Server

<Steps>
  ### Visit the Invite Link

  Go to [interchat.tech/invite](https://interchat.tech/invite) to add InterChat to your Discord server.

  ### Select Your Server

  Choose the server where you want to add InterChat from the dropdown menu.

  <Callout type="warning">
    **Need Admin Permissions:** You must have "Manage Server" permissions to add bots to a Discord server.
  </Callout>

  ### Grant Permissions

  InterChat needs these key permissions to work properly:
  - **Manage Webhooks** - Essential for message delivery
  - **Send Messages** - To relay messages between servers
  - **Manage Messages** - For moderation features
  - **Read Messages** - To see messages to relay

  Click "Authorize" to complete the setup.

  ### Verify Installation

  Type `/help` in any channel to confirm InterChat is working. You should see a response with available commands.
</Steps>

## Step 2: Choose Your Adventure

Now that InterChat is installed, you have two paths forward:

<Tabs items={['Join an Existing Hub', 'Create Your Own Hub']}>
  <Tab>
    **Perfect for:** First-time users who want to experience InterChat immediately

    ### Find a Hub to Join

    1. Visit [interchat.tech/hubs](https://interchat.tech/hubs) in your browser
    2. Browse public hubs by category or search for topics you're interested in
    3. Click "Join Hub" on any hub that catches your eye
    4. Follow the instructions to connect your server

    ### Popular Hub Categories
    - 🎮 **Gaming** - Connect with gamers across different servers
    - 💬 **General Chat** - Casual conversation hubs
    - 🎨 **Creative** - Art, writing, and creative communities
    - 📚 **Learning** - Educational and study groups

    <Callout type="info">
      **Pro Tip:** Look for hubs with high activity and good ratings for the best experience!
    </Callout>
  </Tab>
  <Tab>
    **Perfect for:** Server owners who want to build their own community network

    ### Create Your Hub

    1. Type `/hub create` in any channel
    2. Fill out the creation form:
       - **Hub Name** - Choose something memorable and descriptive
       - **Description** - Explain what your hub is about
       - **Icon URL** - Optional: Add a custom icon

    ### Hub Creation Tips
    - **Be Specific:** "Minecraft Builders Network" is better than "Gaming Hub"
    - **Set Expectations:** Clearly describe what kind of community you're building
    - **Start Small:** You can always expand and customize later

    <Callout type="info">
      **New hubs are private by default.** You can make them public later once you've set up moderation and rules.
    </Callout>
  </Tab>
</Tabs>

## Step 3: Connect Your First Channel

<Steps>
  ### Choose Your Channel

  Pick a channel in your server that you want to connect to the hub. This will be where cross-server messages appear.

  <Callout type="info">
    **Channel Recommendations:**
    - Use a dedicated channel like `#interchat` or `#hub-chat`
    - Avoid connecting your main `#general` channel initially
    - Make sure the channel is active but not overwhelming
  </Callout>

  ### Run the Connect Command

  In your chosen channel, type:
  ```
  /connect hub:YourHubName
  ```

  Replace `YourHubName` with the name of the hub you want to join.

  ### Confirm the Connection

  InterChat will:
  1. Create a webhook in your channel
  2. Connect you to the hub
  3. Send a confirmation message
  4. Show you the hub's welcome message (if configured)
</Steps>

## Step 4: Start Chatting!

🎉 **Congratulations!** You're now connected to a cross-server community. Here's what happens next:

### How It Works
- **Send a message** in your connected channel
- **Watch it appear** in all other connected channels across different servers
- **See messages from others** appear in your channel with their original usernames and avatars

### Your First Message
Try sending a friendly introduction like:
```
Hello from [Your Server Name]! Excited to be part of this hub! 👋
```

## Essential Commands to Remember

| Command | What It Does | Example |
|---------|--------------|---------|
| `/help` | Show all available commands | `/help` |
| `/connection list` | See your server's connections | `/connection list` |
| **Hub Directory** | Find public hubs to join | Visit [interchat.tech/hubs](https://interchat.tech/hubs) |
| `/disconnect` | Remove connection from current channel | `/disconnect` |

## What's Next?

<Callout type="success">
  **You're all set!** You've successfully connected to the InterChat network. Here are some next steps to enhance your experience:
</Callout>

### For Server Members
- **Explore the hub** - Chat with people from other servers
- **Follow hub rules** - Each hub has its own community guidelines
- **Report issues** - Right-click messages to report inappropriate content

### For Server Administrators
- **[Set up moderation](/docs/guides/moderation)** - Learn about safety tools and content filtering
- **[Customize your hub](/docs/guides/hub-management)** - Configure settings, rules, and welcome messages
- **[Manage connections](/docs/guides/connections)** - Advanced connection management and troubleshooting

### For Hub Owners
- **[Make your hub public](/docs/guides/hub-visibility)** - Share your community with the world
- **[Add moderators](/docs/guides/moderation-team)** - Build a moderation team
- **[Configure logging](/docs/guides/logging)** - Set up activity monitoring

## Need Help?

<Callout type="info">
  **Stuck on something?** We're here to help!

  - 📖 **[Browse our guides](/docs/guides)** for detailed tutorials
  - ❓ **[Check the FAQ](/docs/faq)** for common questions
  - 💬 **[Join our support server](https://discord.gg/cgYgC6YZyX)** for live help
</Callout>

---

**Ready for more?** Explore our [comprehensive guides](/docs/guides) to unlock InterChat's full potential!
