import { auth } from '@/auth';
import { db } from '@/lib/prisma';
import { NextResponse } from 'next/server';
import { DonationTierDefinition } from '@/lib/generated/prisma/client';

/**
 * Admin endpoint to process unlinked Ko-fi donations
 * This can be used to retroactively grant premium access to users who linked their accounts
 * after making Ko-fi donations
 */
export async function POST() {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // TODO: Add admin role check here
    // For now, any authenticated user can run this (should be restricted to admins)

    console.log(
      `[Admin] Processing unlinked Ko-fi donations - initiated by user ${session.user.id}`,
    );

    // Find all unprocessed Ko-fi Supporter donations that now have linked users
    const unprocessedDonations = await db.donation.findMany({
      where: {
        processed: false,
        discordUserId: null, // Not yet linked to a Discord user
      },
      include: {
        discordUser: true,
      },
    });

    console.log(`[Admin] Found ${unprocessedDonations.length} unprocessed donations`);

    let processedCount = 0;
    let premiumGrantedCount = 0;

    for (const donation of unprocessedDonations) {
      if (!donation.email) continue;

      // Try to find a Discord user with this email
      const linkedUser = await db.user.findFirst({
        where: { email: donation.email },
        select: {
          id: true,
          donationTierId: true,
          donationExpiresAt: true,
        },
      });

      if (linkedUser) {
        console.log(`[Admin] Found linked user ${linkedUser.id} for donation ${donation.id}`);

        // Update donation to link to Discord user
        await db.donation.update({
          where: { id: donation.id },
          data: {
            discordUserId: linkedUser.id,
            processed: true,
          },
        });

        processedCount++;

        // Infer donation tier based on amount
        const inferredTier = await inferDonationTier(donation.amount);

        if (inferredTier) {
          // Calculate expiration date (30 days from donation date or now, whichever is later)
          const donationDate = new Date(donation.kofiTimestamp);
          const thirtyDaysFromDonation = new Date(
            donationDate.getTime() + 30 * 24 * 60 * 60 * 1000,
          );
          const now = new Date();
          const expiresAt =
            thirtyDaysFromDonation > now
              ? thirtyDaysFromDonation
              : new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

          // Check if user already has a higher or later expiring tier
          const shouldUpdatePremium =
            !linkedUser.donationTierId ||
            !linkedUser.donationExpiresAt ||
            linkedUser.donationExpiresAt < expiresAt;

          if (shouldUpdatePremium) {
            await db.user.update({
              where: { id: linkedUser.id },
              data: {
                donationTierId: inferredTier.id,
                donationExpiresAt: expiresAt,
              },
            });

            premiumGrantedCount++;
            console.log(
              `[Admin] Granted ${inferredTier.name} tier access to user ${linkedUser.id} until ${expiresAt.toISOString()}`,
            );
          } else {
            console.log(
              `[Admin] User ${linkedUser.id} already has premium tier or longer expiry, skipping`,
            );
          }
        }
      }
    }

    console.log(
      `[Admin] Processing complete - ${processedCount} donations processed, ${premiumGrantedCount} premium grants`,
    );

    return NextResponse.json({
      message: 'Unlinked donations processed successfully',
      totalDonations: unprocessedDonations.length,
      processedDonations: processedCount,
      premiumGranted: premiumGrantedCount,
      details: unprocessedDonations.map((d) => ({
        id: d.id,
        email: d.email,
        amount: d.amount,
        currency: d.currency,
        inferredTier: inferDonationTier(d.amount),
        kofiTimestamp: d.kofiTimestamp,
        processed: processedCount > 0,
      })),
    });
  } catch (error) {
    console.error('[Admin] Error processing unlinked donations:', error);
    return NextResponse.json({ error: 'Failed to process unlinked donations' }, { status: 500 });
  }
}

// Helper function to infer donation tier based on amount
async function inferDonationTier(amount: number): Promise<DonationTierDefinition | null> {
  const tiers = await db.donationTierDefinition.findMany({
    orderBy: { price: 'desc' },
  });

  for (const tier of tiers) {
    if (amount >= tier.price) {
      return tier;
    }
  }

  return null;
}

/**
 * Get statistics about unlinked donations
 */
export async function GET() {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // TODO: Add admin role check here

    // Count unprocessed donations
    const unprocessedCount = await db.donation.count({
      where: {
        processed: false,
        discordUserId: null,
      },
    });

    // Count potentially linkable donations (where email exists in users table)
    const potentiallyLinkable = await db.donation.count({
      where: {
        processed: false,
        discordUserId: null,
        email: {
          in: await db.user
            .findMany({
              select: { email: true },
              where: { email: { not: null } },
            })
            .then((users) => users.map((u) => u.email).filter(Boolean) as string[]),
        },
      },
    });

    // Get recent unprocessed donations
    const recentUnprocessed = await db.donation.findMany({
      where: {
        processed: false,
        discordUserId: null,
      },
      select: {
        id: true,
        email: true,
        amount: true,
        currency: true,
        kofiTimestamp: true,
        fromName: true,
      },
      orderBy: { kofiTimestamp: 'desc' },
      take: 10,
    });

    return NextResponse.json({
      unprocessedCount,
      potentiallyLinkable,
      recentUnprocessed,
    });
  } catch (error) {
    console.error('[Admin] Error fetching donation statistics:', error);
    return NextResponse.json({ error: 'Failed to fetch donation statistics' }, { status: 500 });
  }
}
