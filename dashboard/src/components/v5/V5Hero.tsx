"use client";

import { StaticHeroBackground } from "@/components/StaticHeroBackground";
import { Button } from "@/components/ui/button";
import { AnimatedShinyText } from "@/components/ui/animated-shiny-text";
import { ArrowRight, LayoutDashboard, Sparkles, Zap, Command, Gauge } from "lucide-react";
import Link from "next/link";
import { motion } from "motion/react";

export function V5Hero() {
  return (
    <section
      id="v5-hero"
      className="relative overflow-hidden pt-24 md:pt-32 pb-16 md:pb-24 bg-gradient-to-b from-gray-950 to-gray-900 border-b border-gray-800/50"
    >
      <StaticHeroBackground />
      <div className="absolute inset-0 bg-mesh-gradient opacity-15 mix-blend-overlay" />

      <div className="container mx-auto px-4 relative z-10">
        <div className="grid lg:grid-cols-12 gap-10 items-center">
          {/* Left: Copy */}
          <div className="lg:col-span-7">
            <div className="inline-flex items-center gap-2 rounded-[var(--radius-button)] border border-gray-700/60 bg-gray-800/40 px-3 py-1.5 text-xs text-gray-300 shadow-md mb-5">
              <Sparkles className="h-4 w-4 text-primary" />
              <AnimatedShinyText className="font-semibold tracking-wide">Introducing InterChat v5</AnimatedShinyText>
            </div>

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-6xl font-extrabold leading-tight tracking-tight bg-clip-text text-transparent bg-gradient-to-br from-white via-primary-alt to-primary"
            >
              A complete rewrite for speed, clarity, and control
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="mt-5 text-lg md:text-xl text-gray-300 max-w-2xl"
            >
              InterChat v5 brings modern command UIs, dramatically faster performance, and a redesigned dashboard that makes cross-server communication effortless.
            </motion.p>

            {/* Feature Pills */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="mt-6 flex flex-wrap gap-3"
            >
              {[
                { icon: Command, text: "Rewritten Commands" },
                { icon: Gauge, text: "Improved Performance" },
                { icon: LayoutDashboard, text: "New Dashboard Design" },
              ].map(({ icon: Icon, text }) => (
                <div
                  key={text}
                  className="flex items-center gap-2 rounded-[var(--radius)] border border-gray-700/60 bg-gray-800/40 px-3 py-1.5 text-sm text-gray-200 shadow-sm"
                >
                  <Icon className="h-4 w-4 text-primary" />
                  <span className="whitespace-nowrap">{text}</span>
                </div>
              ))}
            </motion.div>

            {/* CTAs */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="mt-8 flex flex-col sm:flex-row gap-4"
            >
              <Button size="lg" className="h-14 px-8">
                <Link href="/invite" className="flex items-center">
                  Add to Discord
                  <ArrowRight className="ml-3 h-5 w-5" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="h-14 px-8">
                <Link href="/dashboard" className="flex items-center">
                  Explore Dashboard
                  <LayoutDashboard className="ml-3 h-5 w-5" />
                </Link>
              </Button>
            </motion.div>

            {/* Subtext */}
            <p className="mt-4 text-sm text-gray-400">
              No setup hassle. Keep full moderation control while you connect communities across servers.
            </p>
          </div>

          {/* Right: Visual */}
          <div className="lg:col-span-5 relative">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.7, delay: 0.15 }}
              className="relative rounded-[var(--radius-button)] border border-gray-700/60 bg-[#313338] shadow-2xl overflow-hidden"
            >
              {/* Decorative glow */}
              <div className="absolute -inset-1 rounded-[var(--radius-button)] bg-gradient-to-r from-primary/30 to-primary-alt/30 blur-xl opacity-25" />

              <div className="relative p-4 md:p-6">
                {/* Simple mock UI to showcase cross-server messages */}
                <div className="space-y-3">
                  {[1,2,3,4,5].map((i) => (
                    <motion.div
                      key={i}
                      initial={{ opacity: 0, x: i % 2 ? -10 : 10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: i * 0.06 }}
                      className={`flex ${i % 2 ? "justify-start" : "justify-end"}`}
                    >
                      <div className="max-w-[80%] rounded-[var(--radius)] px-3 py-2 text-sm border shadow-sm bg-gray-800/70 border-gray-700/60 text-gray-200">
                        <span className="block font-medium text-gray-100">
                          {i % 2 ? "Gaming Hub" : "Tech Hub"}
                        </span>
                        <span className="block text-gray-300">
                          {i === 2 ? "InterChat v5 looks slick!" : i === 4 ? "Latency feels way lower now." : "Cross-server chat just got better."}
                        </span>
                      </div>
                    </motion.div>
                  ))}
                </div>

                {/* Performance callout */}
                <div className="mt-6 rounded-[var(--radius)] border border-gray-700/60 bg-gray-900/60 p-3 text-sm text-gray-300 flex items-center gap-2">
                  <Zap className="h-4 w-4 text-emerald-400" />
                  <span>Up to 2x faster actions with the v5 rewrite</span>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}

