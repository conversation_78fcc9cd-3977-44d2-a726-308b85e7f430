"use client";

import { HubLanguageManagement } from '@/components/dashboard/hubs/HubLanguageManagement';
import { HubNSFWToggle } from '@/components/dashboard/hubs/HubNSFWToggle';
import { HubTagManagement } from '@/components/dashboard/hubs/HubTagManagement';
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import { Globe } from 'lucide-react';

interface HubData {
  id: string;
  name: string;
  description: string;
  private: boolean;
  welcomeMessage: string | null;
  rules: string[];
  bannerUrl: string | null;
  iconUrl: string | null;
  language: string | null;
  nsfw: boolean;
  tags: string[];
  connectionCount: number;
  isOwner: boolean;
  canEdit: boolean;
}

interface HubDiscoverabilityFormProps {
  hubData: HubData;
}

export function HubDiscoverabilityForm({ hubData }: HubDiscoverabilityFormProps) {
  if (!hubData.canEdit) {
    return (
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm shadow-xl">
        <CardContent className="p-8">
          <div className="text-center text-gray-400">
            <Globe className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p>You don&apos;t have permission to edit this hub&apos;s discoverability settings.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-8">
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm shadow-xl">
        <CardHeader>
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
              <Globe className="h-5 w-5 text-white" />
            </div>
            <div>
              <CardTitle className="text-2xl">Discoverability Settings</CardTitle>
              <CardDescription className="text-base">
                Manage tags, language, and content settings to help users find your hub
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-8">
          <HubTagManagement
            hubId={hubData.id}
            currentTags={hubData.tags}
          />
          <div className="border-t border-gray-700/50 pt-8">
            <HubLanguageManagement
              hubId={hubData.id}
              currentLanguage={hubData.language || undefined}
            />
          </div>
          <div className="border-t border-gray-700/50 pt-8">
            <HubNSFWToggle
              hubId={hubData.id}
              currentNsfw={hubData.nsfw}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
