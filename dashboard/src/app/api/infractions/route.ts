"use server";

import { type NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/lib/prisma";
import { z } from "zod/v4";
import { getUserHubPermission } from "@/lib/permissions";
import type {
	InfractionStatus,
	InfractionType,
	Prisma,
} from "@/lib/generated/prisma/client/edge";
import { PermissionLevel } from "@/lib/constants";
import { withAuthRateLimit, withStrictRateLimit } from "@/lib/rate-limit-middleware";
import { ENDPOINT_RATE_LIMITS } from "@/lib/rate-limit-config";

// Schema for creating an infraction
const createInfractionSchema = z
	.object({
		hubId: z.string(),
		type: z.enum(["BLACKLIST", "WARNING"]),
		reason: z.string().min(3).max(500),
		expiresAt: z.string().nullable().optional(),

		// Either userId or serverId must be provided
		userId: z.string().optional(),
		serverId: z.string().optional(),
		serverName: z.string().optional(),
	})
	.refine((data) => data.userId || (data.serverId && data.serverName), {
        error: "Either userId or serverId with serverName must be provided"
    });

// GET all infractions
async function handleGET(request: NextRequest) {
	try {
		const session = await auth();
		if (!session?.user) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		const userId = session.user.id;
		const searchParams = request.nextUrl.searchParams;
		const hubId = searchParams.get("hubId");
		const type = searchParams.get("type");
		const status = searchParams.get("status");
		const targetType = searchParams.get("targetType");
		const targetUserId = searchParams.get("userId");
		const targetServerId = searchParams.get("serverId");

		// Pagination parameters
		const page = Number.parseInt(searchParams.get("page") || "1");
		const limit = Number.parseInt(searchParams.get("limit") || "15");
		const skip = (page - 1) * limit;

		// Get all hubs where the user has at least moderator permissions
		const userHubs = await db.hub.findMany({
			where: {
				OR: [{ ownerId: userId }, { moderators: { some: { userId } } }],
			},
			select: { id: true },
		});

		const hubIds = userHubs.map((hub) => hub.id);

		if (hubIds.length === 0) {
			return NextResponse.json({ infractions: [], total: 0 });
		}

		// Build the where clause
		const whereClause: Prisma.InfractionWhereInput = {
			hubId: hubId ? hubId : { in: hubIds },
		};

		// Add optional filters
		if (type) whereClause.type = type as InfractionType;
		if (status) whereClause.status = status as InfractionStatus;

		// Handle target type filter
		if (targetType === "user") {
			whereClause.userId = { not: null };
		} else if (targetType === "server") {
			whereClause.serverId = { not: null };
		}

		// Handle specific user or server filters
		if (targetUserId) whereClause.userId = targetUserId;
		if (targetServerId) whereClause.serverId = targetServerId;

		// Count total matching infractions for pagination
		const total = await db.infraction.count({
			where: whereClause,
		});

		// Build the query
		const query: Prisma.InfractionFindManyArgs = {
			where: whereClause,
			include: {
				hub: {
					select: {
						id: true,
						name: true,
						iconUrl: true,
					},
				},
				moderator: {
					select: {
						id: true,
						name: true,
						image: true,
					},
				},
				user: {
					select: {
						id: true,
						name: true,
						image: true,
					},
				},
			},
			orderBy: {
				createdAt: "desc",
			},
			skip,
			take: limit,
		};

		const infractions = await db.infraction.findMany({
			...query,
		});

		return NextResponse.json({ infractions, total, page, limit });
	} catch (error) {
		console.log("Error fetching infractions:", error);
		return NextResponse.json(
			{ error: "Failed to fetch infractions" },
			{ status: 500 },
		);
	}
}

// POST (create) a new infraction
async function handlePOST(request: NextRequest) {
	try {
		const session = await auth();
		if (!session?.user) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Parse and validate the request body
		const body = await request.json();
		const validation = createInfractionSchema.safeParse(body);

		if (!validation.success) {
			return NextResponse.json(
				{ error: "Invalid input", details: validation.error.message },
				{ status: 400 },
			);
		}

		const { hubId, type, reason, expiresAt, userId, serverId, serverName } =
			validation.data;

		// Check if the user has permission to create infractions for this hub
		const permissionLevel = await getUserHubPermission(session.user.id, hubId);
		if (permissionLevel < PermissionLevel.MODERATOR) {
			return NextResponse.json({ error: "Forbidden" }, { status: 403 });
		}

		// Create the infraction
		const infraction = await db.infraction.create({
			data: {
				hubId,
				type,
				reason,
				moderatorId: session.user.id,
				expiresAt: expiresAt ? new Date(expiresAt) : null,
				userId,
				serverId,
				serverName,
			},
			include: {
				hub: {
					select: {
						id: true,
						name: true,
						iconUrl: true,
					},
				},
				moderator: {
					select: {
						id: true,
						name: true,
						image: true,
					},
				},
				user: {
					select: {
						id: true,
						name: true,
						image: true,
					},
				},
			},
		});

		return NextResponse.json({ infraction }, { status: 201 });
	} catch (error) {
		console.error("Error creating infraction:", error);
		return NextResponse.json(
			{ error: "Failed to create infraction" },
			{ status: 500 },
		);
	}
}

// Apply rate limiting to the handlers
export const GET = withAuthRateLimit(handleGET, {
	tier: ENDPOINT_RATE_LIMITS.MODERATION.LIST_INFRACTIONS,
});

export const POST = withStrictRateLimit(handlePOST, {
	tier: ENDPOINT_RATE_LIMITS.MODERATION.CREATE_INFRACTION,
	customMessage: "Infraction creation rate limit exceeded. Please wait before creating another infraction.",
});
