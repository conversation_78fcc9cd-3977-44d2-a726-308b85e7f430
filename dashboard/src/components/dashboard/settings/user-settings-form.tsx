"use client";

import { useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { AlertTriangle } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { SUPPORTED_LANGUAGES } from "@/lib/constants";
import { UnsavedChangesPrompt } from '@/components/dashboard/unsaved-changes-prompt';

interface UserSettingsFormProps {
  userId: string;
  initialMentionOnReply: boolean;
  initialLocale: string | null;
  initialShowNsfwHubs: boolean;
}

export function UserSettingsForm({
  userId,
  initialMentionOnReply,
  initialLocale,
  initialShowNsfwHubs,
}: UserSettingsFormProps) {
  const { toast } = useToast();
  const [mentionOnReply, setMentionOnReply] = useState(initialMentionOnReply);
  const [locale, setLocale] = useState<string>(initialLocale || "en");
  const [showNsfwHubs, setShowNsfwHubs] = useState(initialShowNsfwHubs);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Track changes
  useEffect(() => {
    const changed =
      mentionOnReply !== initialMentionOnReply ||
      locale !== (initialLocale || "en") ||
      showNsfwHubs !== initialShowNsfwHubs;
    setHasChanges(changed);
  }, [mentionOnReply, locale, showNsfwHubs, initialMentionOnReply, initialLocale, initialShowNsfwHubs]);

  const handleSaveSettings = async () => {
    if (!hasChanges) return;

    setIsSaving(true);
    try {
      const response = await fetch(`/api/users/${userId}/settings`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          mentionOnReply,
          locale,
          showNsfwHubs,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to save settings");
      }

      toast({
        title: "Settings saved",
        description: "Your settings have been updated successfully.",
      });

      // Update initial values to reflect the new state
      // This will reset the hasChanges flag
      setHasChanges(false);
    } catch (error) {
      console.error("Error saving settings:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to save settings",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Reset form to original values
  const resetForm = () => {
    setMentionOnReply(initialMentionOnReply);
    setLocale(initialLocale || "en");
    setShowNsfwHubs(initialShowNsfwHubs);
    setHasChanges(false);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="space-y-0.5">
          <Label htmlFor="mention-on-reply">Mention on Reply</Label>
          <p className="text-sm text-gray-400">
            Get @ mentioned when someone replies to your messages
          </p>
        </div>
        <Switch
          id="mention-on-reply"
          checked={mentionOnReply}
          onCheckedChange={setMentionOnReply}
          className="data-[state=checked]:bg-indigo-600"
        />
      </div>

      <div className="flex items-center justify-between">
        <div className="space-y-0.5">
          <div className="flex items-center gap-2">
            <Label htmlFor="show-nsfw-hubs">Show NSFW Hubs</Label>
            <AlertTriangle className="h-4 w-4 text-orange-400" />
          </div>
          <p className="text-sm text-gray-400">
            Allow NSFW (adult content) hubs to appear in search results and recommendations
          </p>
          <p className="text-xs text-orange-300">
            ⚠️ Only enable this if you are 18+ and want to see adult content
          </p>
        </div>
        <Switch
          id="show-nsfw-hubs"
          checked={showNsfwHubs}
          onCheckedChange={setShowNsfwHubs}
          className="data-[state=checked]:bg-orange-600"
        />
      </div>



      <div className="space-y-2">
        <Label htmlFor="language-select">Language</Label>
        <p className="text-sm text-gray-400 mb-2">
          Select your preferred language (currently only affects the bot
          responses, not the website)
        </p>
        <Select value={locale} onValueChange={(value) => setLocale(value)}>
          <SelectTrigger
            id="language-select"
            className="w-full md:w-[240px] bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50"
          >
            <SelectValue placeholder="Select language" />
          </SelectTrigger>
          <SelectContent className="bg-gray-900 border border-gray-800">
            {SUPPORTED_LANGUAGES.map((language) => (
              <SelectItem key={language.code} value={language.code}>
                {language.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Unsaved Changes Prompt */}
      <UnsavedChangesPrompt
        isVisible={hasChanges}
        onSave={handleSaveSettings}
        onReset={resetForm}
        isSubmitting={isSaving}
        saveLabel="Save Settings"
        resetLabel="Reset Settings"
        message="Careful! You have unsaved user setting changes."
      />
    </div>
  );
}
