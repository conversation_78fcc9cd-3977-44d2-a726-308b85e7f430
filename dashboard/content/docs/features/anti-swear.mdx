---
title: Anti-Swear
description: Learn how to configure your hub's anti-swear filters
---


Control inappropriate language using `/hub config anti-swear`. The interface provides intuitive buttons for:

- Adding new filter rules
- Removing existing rules
- Setting violation actions
- Editing filtered words

### Creating Filter Rules

1. Click the "Add Rule" button to create a new rule
2. Select an existing rule from the dropdown menu
3. Enter the word to filter in the modal
4. Choose your desired match pattern and action

#### Match Pattern Reference

| Pattern  | Description       | Example Result                                    |
| -------- | ----------------- | ------------------------------------------------- |
| `word`   | Exact word match  | "heck" → blocks only "heck"                       |
| `word*`  | Prefix matching   | "heck\*" → blocks "hecking", "hecked"             |
| `*word`  | Suffix matching   | "\*heck" → blocks "reheck", "unheck"              |
| `*word*` | Contains matching | "\*heck\*" → blocks "hecking", "unheck", "reheck" |

### Available Actions

| Action       | Effect                                               |
| ------------ | ---------------------------------------------------- |
| `Block`      | Prevents message from being sent                     |
| `Blacklist`  | Automatically blacklists sender (10 minute duration) |
| `Send Alert` | Notifies hub about the violation                     |

<Callout type="warn">
  The `Send Alert` action requires enabled `networkAlerts` logging. See the
  [Logging Section](/docs/features/logging).
</Callout>
