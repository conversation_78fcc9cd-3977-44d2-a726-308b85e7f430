---
title: Blacklisting
description: Ban problematic users and servers from your hubs
---


# Blacklisting

Blacklisting is a powerful moderation tool that allows you to ban problematic users or entire servers from your hub.

## Understanding Blacklisting

When you blacklist a user or server:

- Their messages will no longer appear in any channels connected to your hub
- They cannot join your hub with new connections
- Existing connections from blacklisted servers will be automatically disconnected

<Callout type="info">
  Blacklisting is hub-specific. A user or server blacklisted in one hub can still participate in other hubs.
</Callout>

## Types of Blacklisting

<Tabs items={['User Blacklisting', 'Server Blacklisting']}>
  <Tab>
    User blacklisting targets specific individuals:
    
    - Blocks a specific user across all servers in the hub
    - Useful for dealing with problematic individuals
    - Can be temporary or permanent
    
    Use `/blacklist user` to blacklist a user.
  </Tab>
  <Tab>
    Server blacklisting targets entire Discord servers:
    
    - Disconnects the specified server from the hub
    - Prevents the server from rejoining the hub
    - Useful for dealing with servers that consistently violate rules
    
    Use `/blacklist server` to blacklist a server.
  </Tab>
</Tabs>

## Blacklisting Commands

### Blacklisting Users

To blacklist a user:

1. Use the `/blacklist user` command
2. Specify the hub name
3. Enter the user ID or mention the user
4. Provide a reason for the blacklist
5. Optionally, set a duration for temporary blacklists

Example: `/blacklist user hub:Gaming Discussion user:@ProblemUser reason:Spamming duration:7d`

### Blacklisting Servers

To blacklist a server:

1. Use the `/blacklist server` command
2. Specify the hub name
3. Enter the server ID
4. Provide a reason for the blacklist
5. Optionally, set a duration for temporary blacklists

Example: `/blacklist server hub:Gaming Discussion server:123456789012345678 reason:Repeated rule violations`

## Managing Blacklists

### Viewing Blacklists

To view all blacklisted users and servers in your hub:

- Use `/blacklist list` to see all active blacklists
- This shows who was blacklisted, by whom, for what reason, and when it expires (if applicable)

### Removing Blacklists

To remove a blacklist before it expires:

- Use `/unblacklist user` to unban a user
- Use `/unblacklist server` to unban a server

<Callout type="warning">
  Only hub owners, managers, and moderators can manage blacklists. Server administrators cannot override blacklists set by hub staff.
</Callout>

## Temporary vs. Permanent Blacklists

You can create two types of blacklists:

1. **Temporary Blacklists**: Include a duration (e.g., "1d" for one day)
   - Automatically expire after the specified time
   - Good for minor or first-time offenses

2. **Permanent Blacklists**: No duration specified
   - Never expire unless manually removed
   - Appropriate for serious or repeated violations

## Best Practices

For effective use of blacklisting:

- Always provide clear reasons when blacklisting
- Use temporary blacklists for minor offenses
- Reserve permanent blacklists for serious violations
- Document blacklisting decisions in your moderation logs
- Communicate with other server administrators when blacklisting their servers
- Regularly review your blacklist to ensure it remains appropriate
