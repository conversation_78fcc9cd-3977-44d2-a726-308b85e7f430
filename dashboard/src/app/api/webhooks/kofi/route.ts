import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod/v4';
import { db } from '@/lib/prisma';
import { discordEmailVerificationService } from '@/lib/discord-email-verification';
import { inferDonationTier, getDonationTierByName } from '@/lib/donation-utils';

/**
 * Ko-fi webhook payload schema
 * Based on Ko-fi webhook documentation and real payload examples
 */
const kofiPayloadSchema = z.object({
  verification_token: z.uuid(),
  message_id: z.uuid(),
  timestamp: z.iso.datetime(),
  type: z.enum(['Donation', 'Subscription', 'Commission', 'Shop Order']),
  is_public: z.boolean(),
  from_name: z.string().min(1, 'From name cannot be empty'),
  message: z.string().nullable().optional(),
  amount: z.string().regex(/^\d+\.\d{2}$/, 'Amount must be in decimal format (e.g., "3.00")'),
  url: z.url(),
  email: z.email(),
  currency: z.string().length(3, 'Currency must be a 3-letter code'),
  kofi_transaction_id: z.uuid(),

  // Subscription-specific fields
  is_subscription_payment: z.boolean().optional(),
  is_first_subscription_payment: z.boolean().optional(),
  tier_name: z.string().nullish(), // e.g., "Supporter"

  shop_items: z.array(z.any()).nullable().optional(),
  shipping: z
    .object({
      full_name: z.string(),
      street_address: z.string(),
      city: z.string(),
      state_or_province: z.string(),
      postal_code: z.string(),
      country: z.string(),
      country_code: z.string(),
      telephone: z.string().optional(),
    })
    .nullable()
    .optional(),
});

type KofiPayload = z.infer<typeof kofiPayloadSchema>;

/**
 * Determine donation tier based on Ko-Fi tier name or amount
 */
async function getDonationTierFromPayload(payload: KofiPayload) {
  // First try to determine tier from Ko-Fi tier name (for subscriptions)
  if (payload.tier_name) {
    const tierByName = await getDonationTierByName(payload.tier_name);
    if (tierByName) {
      return tierByName;
    }
    console.warn(`Unknown Ko-Fi tier name: ${payload.tier_name}`);
  }

  // Fallback to amount-based tier determination for regular donations
  const amount = parseFloat(payload.amount);
  return await inferDonationTier(amount);
}

/**
 * Parse and validate Ko-fi webhook data
 */
function parseKofiWebhookData(dataField: string): KofiPayload | null {
  try {
    const parsed = JSON.parse(dataField);
    const result = kofiPayloadSchema.safeParse(parsed);
    if (!result.success) {
      console.error('Ko-fi payload validation failed:', result.error.issues);
      return null;
    }
    return result.data;
  } catch (error) {
    console.error('Failed to parse Ko-fi webhook JSON:', error);
    return null;
  }
}

/**
 * Validate Ko-fi webhook verification token
 */
function validateKofiWebhookToken(providedToken: string, expectedToken: string): boolean {
  if (!providedToken || !expectedToken) {
    return false;
  }
  return providedToken === expectedToken;
}

/**
 * Find linked Discord user by Ko-fi email using verified Discord emails and donation emails
 * This ensures only users with verified Discord emails or set donation emails can receive premium benefits
 */
async function findLinkedDiscordUser(kofiEmail: string): Promise<string | null> {
  if (!kofiEmail) return null;

  // First try to find user by verified Discord email
  const userByVerifiedEmail = await discordEmailVerificationService.findUserByVerifiedEmail(kofiEmail);
  if (userByVerifiedEmail) {
    return userByVerifiedEmail;
  }

  // Then try to find user by donation email
  const userByDonationEmail = await db.user.findFirst({
    where: {
      donationEmail: kofiEmail,
    },
    select: {
      id: true,
    },
  });

  return userByDonationEmail?.id || null;
}

/**
 * Grant donation tier access to Discord user
 */
async function grantDonationTierAccess(
  userId: string,
  tier: { id: string; name: string; description: string; price: number; createdAt: Date; updatedAt: Date },
  donationEmail?: string,
): Promise<void> {
  console.log(`Granting donation tier access to user ${userId} for tier: ${tier.name}`);

  // Calculate expiration date (30 days from now for all tiers)
  const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);

  // Get user's OAuth email to set as default donation email if not already set
  const user = await db.user.findUnique({
    where: { id: userId },
    select: { email: true, donationEmail: true, badges: true },
  });

  const updateData: {
    donationTierId: string;
    donationExpiresAt: Date;
    donationEmail?: string;
    badges?: { push: 'SUPPORTER' };
  } = {
    donationTierId: tier.id,
    donationExpiresAt: expiresAt,
  };

  // Only add SUPPORTER badge for SUPPORTER tier (could extend for other tiers)
  if (tier.name === 'SUPPORTER' && !user?.badges.includes('SUPPORTER')) {
    updateData.badges = { push: 'SUPPORTER' } as const;
  }

  // Set donation email priority:
  // 1. Use provided donation email (from Ko-fi)
  // 2. Use existing donation email if already set
  // 3. Default to OAuth email
  if (donationEmail) {
    updateData.donationEmail = donationEmail;
  } else if (!user?.donationEmail && user?.email) {
    updateData.donationEmail = user.email;
  }

  await db.user.update({
    where: { id: userId },
    data: updateData,
  });

  console.log(
    `Donation tier granted to user ${userId}, expires: ${expiresAt?.toISOString() || 'never'}`,
  );
}

/**
 * Send donation tier notification (placeholder for future Discord bot integration)
 */
async function sendDonationTierNotification(
  userId: string,
  tier: { name: string; id: string; description: string; price: number; createdAt: Date; updatedAt: Date },
): Promise<void> {
  console.log(`[Notification] User ${userId} granted ${tier.name} tier access`);

  // TODO: Integrate with Discord bot to send DM or channel notification
  // This would require calling the Discord bot API or using a shared message queue
  // For now, we'll just log the notification

  const message = `🎉 Welcome to the ${tier.name} tier! You now have access to exclusive InterChat features.`;

  console.log(`[Notification] Would send to user ${userId}: ${message}`);
}

/**
 * Process Ko-fi donation and update user donation tier
 */
async function processDonation(payload: KofiPayload): Promise<void> {
  const amount = parseFloat(payload.amount);
  const donationTier = await getDonationTierFromPayload(payload);

  console.log(`Processing Ko-fi donation: ${amount} ${payload.currency} from ${payload.from_name}`);
  console.log(`Type: ${payload.type}, Tier Name: ${payload.tier_name || 'None'}`);
  console.log(`Determined Donation Tier: ${donationTier || 'None'}`);

  try {
    // Try to find linked Discord user
    const linkedUserId = await findLinkedDiscordUser(payload.email || '');

    // Store donation record
    const donation = await db.donation.create({
      data: {
        kofiTransactionId: payload.kofi_transaction_id,
        messageId: payload.message_id,
        amount,
        currency: payload.currency,
        fromName: payload.from_name,
        message: payload.message,
        email: payload.email,
        isPublic: payload.is_public,
        kofiTimestamp: new Date(payload.timestamp),
        kofiUrl: payload.url,
        discordUserId: linkedUserId,
        processed: false,
        donationTierId: donationTier?.id,
      },
    });

    console.log(`Donation record created with ID: ${donation.id}`);

    // Grant donation tier access if a tier is determined
    if (donationTier) {
      console.log(`Donation tier ${donationTier} detected`);

      if (linkedUserId) {
        console.log(`Granting donation tier access to linked user: ${linkedUserId}`);

        // Grant donation tier access
        await grantDonationTierAccess(linkedUserId, donationTier, payload.email || undefined);

        // Send notification
        await sendDonationTierNotification(linkedUserId, donationTier);

        // Mark donation as processed
        await db.donation.update({
          where: { id: donation.id },
          data: { processed: true },
        });

        console.log(`Donation benefits granted and donation marked as processed`);
      } else {
        console.log(`No linked Discord user found for email: ${payload.email || 'none'}`);

        // Create a pending claim for tier-eligible donations
        if (donationTier && payload.email) {
          const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days to claim

          await db.pendingClaim.upsert({
            where: { kofiTransactionId: payload.kofi_transaction_id },
            create: {
              kofiTransactionId: payload.kofi_transaction_id,
              email: payload.email,
              tierName: payload.tier_name ?? null,
              amount,
              currency: payload.currency,
              fromName: payload.from_name,
              kofiUrl: payload.url,
              expiresAt,
            },
            update: {
              email: payload.email,
              tierName: payload.tier_name ?? null,
              amount,
              currency: payload.currency,
              fromName: payload.from_name,
              expiresAt,
              kofiUrl: payload.url,
            },
          });

          console.log(`Created pending claim for tier ${donationTier} - user can claim with /claim command`);
        }

        console.log(
          `Donation stored but benefits not granted - user needs to link account or use /claim`,
        );
      }
    } else {
      // Mark non-tier donations as processed
      await db.donation.update({
        where: { id: donation.id },
        data: { processed: true },
      });
    }

    console.log('Successfully processed Ko-fi donation');
  } catch (error) {
    console.error('Error processing donation:', error);
    throw error;
  }
}

/**
 * Ko-fi webhook endpoint
 * Handles Ko-fi webhook requests for donation processing
 */
export async function POST(request: NextRequest) {
  try {
    console.log(`Processing Ko-fi webhook request from ${request.headers.get('x-forwarded-for')}`);

    // Verify content type (Ko-fi sends application/x-www-form-urlencoded)
    const contentType = request.headers.get('content-type');
    if (!contentType?.includes('application/x-www-form-urlencoded')) {
      console.warn(
        `Invalid content type: ${contentType}. Expected application/x-www-form-urlencoded`,
      );
      return NextResponse.json({ error: 'Invalid content type' }, { status: 400 });
    }

    // Get verification token from environment
    const expectedToken = process.env.KOFI_VERIFICATION_TOKEN;
    if (!expectedToken) {
      console.error('Ko-fi verification token not configured');
      return NextResponse.json({ error: 'Service configuration error' }, { status: 500 });
    }

    // Ko-fi sends data as application/x-www-form-urlencoded with a 'data' field containing JSON
    const formData = await request.formData();
    const dataField = formData.get('data');

    if (!dataField || typeof dataField !== 'string') {
      console.warn('Missing or invalid data field in Ko-fi webhook');
      return NextResponse.json({ error: 'Missing data field' }, { status: 400 });
    }

    // Parse and validate the webhook payload
    const kofiPayload = parseKofiWebhookData(dataField);
    if (!kofiPayload) {
      return NextResponse.json({ error: 'Invalid webhook payload structure' }, { status: 400 });
    }

    // Verify webhook token
    if (!validateKofiWebhookToken(kofiPayload.verification_token, expectedToken)) {
      console.warn('Ko-fi webhook token verification failed');
      return NextResponse.json({ error: 'Invalid verification token' }, { status: 401 });
    }

    // Check for duplicate transaction
    const existingDonation = await db.donation.findUnique({
      where: { kofiTransactionId: kofiPayload.kofi_transaction_id },
    });

    if (existingDonation) {
      console.warn(`Duplicate Ko-fi transaction: ${kofiPayload.kofi_transaction_id}`);
      return NextResponse.json({ message: 'Transaction already processed' });
    }

    // Process the donation
    await processDonation(kofiPayload);

    console.log('Successfully processed Ko-fi webhook');
    return NextResponse.json({ message: 'Webhook processed successfully' });
  } catch (error) {
    console.error('Error processing Ko-fi webhook:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
