---
title: Moderation
description: Keep your hubs safe with InterChat's moderation tools
---


# Moderation

InterChat provides powerful moderation tools to help you maintain a safe and friendly environment in your hubs.

## Moderation Roles

InterChat has three levels of moderation permissions:

1. **Hub Owner** - Full control over the hub and all moderation actions
2. **Hub Manager** - Can manage most hub settings and perform all moderation actions
3. **Hub Moderator** - Can perform basic moderation actions like blacklisting users

<Callout type="info">
  Server administrators always have full control over their own server's connections, regardless of their hub role.
</Callout>

## Adding Moderators

To add a moderator to your hub:

1. Use the `/hub moderator add` command
2. Specify the user you want to add
3. Choose their role (moderator or manager)

You can view all moderators with `/hub moderator list` and remove them with `/hub moderator remove`.

## Moderation Tools

<Tabs items={['Content Filtering', 'User Management', 'Logging']}>
  <Tab>
    InterChat offers several content filtering options:

    - **Anti-Swear**: Block messages containing specific words or patterns
      - Configure with `/hub config anti-swear`

    - **NSFW Detection**: Automatically detect and block NSFW content
      - Toggle with `/hub config settings`

    - **Spam Protection**: Prevent spam messages
      - Toggle with `/hub config settings`
  </Tab>
  <Tab>
    Manage users in your hub with these tools:

    - **Warnings**: Issue formal warnings to users who violate rules
      - Use `/warn` to warn a user
      - Warnings are recorded in the user's infraction history
      - Warnings are logged in your moderation logs

    - **Blacklisting**: Ban problematic users from your hub
      - Use `/blacklist user` to ban a user
      - Use `/unblacklist user` to unban

    - **Server Blacklisting**: Ban entire servers from your hub
      - Use `/blacklist server` to ban a server
      - Use `/unblacklist server` to unban

    - **Infractions**: View a user's history of rule violations
      - Use `/hub infractions` to view all infractions including warnings
  </Tab>
  <Tab>
    Set up logging to monitor activity in your hub:

    - **Moderation Logs** (`modLogs`): Record all moderation actions like blacklisting and warnings
    - **Join/Leave Logs** (`joinLeaves`): Track servers joining or leaving your hub
    - **Report Logs** (`reports`): Receive notifications when content is reported
    - **Appeal Logs** (`appeals`): Track blacklist appeal requests
    - **Network Alert Logs** (`networkAlerts`): Receive important system notifications

    Configure logging with `/hub config logging`
  </Tab>
</Tabs>

## Handling Reports

When users report content in your hub:

1. The report is sent to your configured logging channel
2. Moderators can review the report and take appropriate action
3. Actions include:
   - Blacklisting the user
   - Blacklisting the server
   - Deleting the message
   - Warning the user
   - Marking the report as resolved

<Callout type="info">
  Set up a dedicated channel for report logs to ensure moderators don't miss any reports.
</Callout>

## Hub Rules

Establish clear rules for your hub to set expectations for all participants:

1. Use `/hub config rules` to set up your hub rules
2. Users will be shown these rules when they join your hub
3. Reference these rules when taking moderation actions

## Best Practices

For effective moderation:

- Assign multiple moderators across different time zones
- Set up comprehensive logging
- Establish clear, specific rules
- Be consistent in enforcement
- Communicate with other server administrators in your hub
- Regularly review and update your moderation settings
