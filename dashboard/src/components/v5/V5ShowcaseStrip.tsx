"use client";

import { motion } from "motion/react";
import { Globe, Network, Share2, MessageSquare, Link2, Server } from "lucide-react";

const items = [
  { icon: Globe, text: "Cross-Server Messaging" },
  { icon: Network, text: "Hub Ecosystem" },
  { icon: Share2, text: "Webhook-powered Broadcasts" },
  { icon: Server, text: "Per-Channel Links" },
  { icon: MessageSquare, text: "Real-time Delivery" },
  { icon: Link2, text: "Simple Connections" },
];

export function V5ShowcaseStrip() {
  return (
    <section className="relative py-10 bg-gray-950/60 border-b border-gray-800/50">
      <div className="container mx-auto px-4 overflow-hidden">
        <div className="flex gap-6 animate-[scroll_30s_linear_infinite] [--scroll-distance:50%]">
          {items.concat(items).map((item, idx) => (
            <motion.div
              key={`${item.text}-${idx}`}
              className="flex items-center gap-2 rounded-[var(--radius)] border border-gray-700/60 bg-gray-800/40 px-4 py-2 text-sm text-gray-200 shadow-sm"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: idx * 0.04 }}
            >
              <item.icon className="h-4 w-4 text-primary" />
              <span>{item.text}</span>
            </motion.div>
          ))}
        </div>
      </div>
      <style jsx>{`
        @keyframes scroll {
          0% { transform: translateX(0); }
          100% { transform: translateX(calc(var(--scroll-distance) * -1)); }
        }
      `}</style>
    </section>
  );
}

