'use client';

import type { BasicHubConnection } from '@/app/dashboard/hubs/[hubId]/connections/client';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { formatDistanceToNow } from 'date-fns';
import {
    Ban,
    Clock,
    Hash,
    Home,
    MoreVertical,
    PlusCircle,
    Search,
    Server,
    Trash,
    Users,
    Wifi,
    WifiOff,
    X
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';

interface ServerData {
  id: string;
  name: string | null;
  iconUrl: string | null;
}

interface SimpleConnectionsListProps {
  connections: (BasicHubConnection & { server: ServerData | null })[];
  hubId: string;
  canManage: boolean;
  isLoading?: boolean;
  onConnectionRemoved?: (connectionId: string) => void;
}

export function ConnectionsList({
  connections,
  hubId,
  canManage,
  isLoading = false,
  onConnectionRemoved,
}: SimpleConnectionsListProps) {
  const { toast } = useToast();
  const router = useRouter();

  // Simple state management
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredConnections, setFilteredConnections] = useState(connections);

  // Update filtered connections when connections or search changes
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredConnections(connections);
      return;
    }

    const filtered = connections.filter((connection) =>
      connection.server?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      connection.serverId.toLowerCase().includes(searchQuery.toLowerCase()) ||
      connection.channelId.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredConnections(filtered);
  }, [connections, searchQuery]);

  // Action handlers
  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        toast({
          description: `${label} copied to clipboard`,
        });
      },
      (err) => {
        console.error('Could not copy text: ', err);
        toast({
          variant: 'destructive',
          description: 'Failed to copy to clipboard',
        });
      },
    );
  };

  const handleRemoveConnection = async (connectionId: string, serverName: string) => {
    if (confirm(`Are you sure you want to remove the connection to "${serverName}"?`)) {
      try {
        const response = await fetch(`/api/dashboard/connections/${connectionId}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error('Failed to remove connection');
        }

        toast({
          description: `Connection to ${serverName} removed`,
          variant: 'success',
        });

        // Trigger callback to update the UI
        if (onConnectionRemoved) {
          onConnectionRemoved(connectionId);
        }
      } catch (error) {
        console.error('Error removing connection:', error);
        toast({
          title: 'Error',
          description: 'Failed to remove connection. Please try again.',
          variant: 'destructive',
        });
      }
    }
  };

  const handleBlacklistServer = (serverId: string) => {
    const params = new URLSearchParams();
    params.set('hubId', hubId);
    params.set('serverId', serverId);
    router.push(`/dashboard/moderation/blacklist/add?${params.toString()}`);
  };

  const connectedCount = connections.filter((c) => c.connected).length;
  const pausedCount = connections.filter((c) => !c.connected).length;

  if (isLoading) {
    return (
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle>Server Connections</CardTitle>
          <CardDescription>Loading connections...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 6 }).map((_, i) => (
              <div
                key={i}
                className="flex items-center gap-4 p-4 rounded-lg border border-gray-800/50"
              >
                <Skeleton className="h-12 w-12 rounded-lg" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-24" />
                </div>
                <Skeleton className="h-8 w-8" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
      <CardHeader className="pb-4 border-b border-gray-800/30">
        <div className="flex flex-col gap-4">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
            <div className="space-y-1">
              <CardTitle className="text-xl font-bold text-white flex items-center gap-2">
                <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center">
                  <Server className="h-4 w-4 text-white" />
                </div>
                Server Connections
              </CardTitle>
              <CardDescription className="text-gray-400 text-sm">
                {connections.length} server{connections.length !== 1 ? 's' : ''} connected to this hub
              </CardDescription>
            </div>

            {canManage && (
              <Button
                asChild
                size="sm"
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 border-none h-11 px-4 text-sm font-medium w-full sm:w-auto"
              >
                <Link href={`/dashboard?hubId=${hubId}`}>
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Connect Server
                </Link>
              </Button>
            )}
          </div>

          {/* Stats */}
          <div className="flex items-center gap-6 text-sm">
            <div className="flex items-center gap-2 px-3 py-1.5 rounded-full bg-green-500/10 border border-green-500/20">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-green-400 font-medium">{connectedCount} active</span>
            </div>
            <div className="flex items-center gap-2 px-3 py-1.5 rounded-full bg-amber-500/10 border border-amber-500/20">
              <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
              <span className="text-amber-400 font-medium">{pausedCount} paused</span>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6 pt-6">
        {/* Simple Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search servers by name or ID..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 h-12 bg-gray-800/30 border-gray-700/50 text-white placeholder:text-gray-500 focus:border-blue-500/50 rounded-xl"
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0 hover:bg-gray-700/50 rounded-lg"
              onClick={() => setSearchQuery('')}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Connections List */}
        {connections.length === 0 ? (
          <div className="text-center py-12 px-4">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-800/50 flex items-center justify-center">
              <Home className="h-8 w-8 text-gray-500" />
            </div>
            <h3 className="text-lg font-medium text-white mb-2">No Connections</h3>
            <p className="text-gray-400 mb-6 max-w-sm mx-auto">
              This hub is not connected to any Discord servers yet. Connect your first server to start building your community.
            </p>
            {canManage && (
              <Button
                asChild
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-600/80 hover:to-indigo-600/80 border-none h-11 px-4"
              >
                <Link href={`/dashboard?hubId=${hubId}`}>
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Connect Your First Server
                </Link>
              </Button>
            )}
          </div>
        ) : filteredConnections.length === 0 ? (
          <div className="text-center py-12 px-4">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-800/50 flex items-center justify-center">
              <Search className="h-8 w-8 text-gray-500" />
            </div>
            <h3 className="text-lg font-medium text-white mb-2">No Results Found</h3>
            <p className="text-gray-400 mb-4">
              No servers match your search criteria.
            </p>
            <Button
              variant="outline"
              onClick={() => setSearchQuery('')}
              className="border-gray-700/50 text-gray-300 hover:bg-gray-800/50 hover:text-white h-10 px-4"
            >
              Clear Search
            </Button>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredConnections.map((connection) => (
              <ConnectionItem
                key={connection.id}
                connection={connection}
                canManage={canManage}
                onCopy={copyToClipboard}
                onRemove={handleRemoveConnection}
                onBlacklist={handleBlacklistServer}
              />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Separate component for each connection item
interface ConnectionItemProps {
  connection: BasicHubConnection & { server: ServerData | null };
  canManage: boolean;
  onCopy: (text: string, label: string) => void;
  onRemove: (connectionId: string, serverName: string) => void;
  onBlacklist: (serverId: string) => void;
}

function ConnectionItem({ connection, canManage, onCopy, onRemove, onBlacklist }: ConnectionItemProps) {
  return (
    <div className="group relative rounded-xl border border-gray-800/50 bg-gradient-to-r from-gray-800/20 to-gray-800/10 hover:from-gray-800/40 hover:to-gray-800/30 hover:border-gray-700/70 transition-all duration-200 p-4">
      <div className="flex items-center gap-4">
        {/* Server Icon */}
        <div className="relative flex-shrink-0">
          <div className="h-12 w-12 sm:h-14 sm:w-14 rounded-xl overflow-hidden border border-gray-700/50 bg-gradient-to-br from-gray-700 to-gray-800">
            {connection.server?.iconUrl ? (
              <Image
                src={connection.server.iconUrl}
                alt={connection.server.name || 'Server'}
                width={56}
                height={56}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <Users className="h-6 w-6 text-gray-400" />
              </div>
            )}
          </div>

          {/* Status Indicator */}
          <div className="absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-gray-900 flex items-center justify-center">
            {connection.connected ? (
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            ) : (
              <div className="w-2 h-2 bg-amber-500 rounded-full" />
            )}
          </div>
        </div>

        {/* Server Info */}
        <div className="flex-1 min-w-0">
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-2">
            <h4 className="font-semibold text-white text-base truncate">
              {connection.server?.name || 'Unknown Server'}
            </h4>
            <Badge
              variant={connection.connected ? 'default' : 'secondary'}
              className={`w-fit text-xs font-medium px-2 py-1 rounded-full ${
                connection.connected
                  ? 'bg-green-500/20 text-green-300 border-green-500/40'
                  : 'bg-amber-500/20 text-amber-300 border-amber-500/40'
              }`}
            >
              {connection.connected ? (
                <>
                  <Wifi className="h-3 w-3 mr-1" />
                  Active
                </>
              ) : (
                <>
                  <WifiOff className="h-3 w-3 mr-1" />
                  Paused
                </>
              )}
            </Badge>
          </div>

          {/* Connection details */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-sm text-gray-400">
            <div className="flex items-center gap-1">
              <Hash className="h-3 w-3 flex-shrink-0" />
              <span className="font-mono text-xs truncate">{connection.channelId}</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3 flex-shrink-0" />
              <span className="text-xs truncate">
                {formatDistanceToNow(new Date(connection.lastActive), { addSuffix: true })}
              </span>
            </div>
          </div>
        </div>

        {/* Actions Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 hover:bg-gray-700/50 rounded-lg opacity-0 group-hover:opacity-100 sm:opacity-100 transition-opacity"
            >
              <MoreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="bg-gray-900 border-gray-800">
            <DropdownMenuItem
              onClick={() => onCopy(connection.serverId, 'Server ID')}
              className="text-gray-300 hover:bg-gray-800 hover:text-white"
            >
              <Server className="h-4 w-4 mr-2" />
              Copy Server ID
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => onCopy(connection.channelId, 'Channel ID')}
              className="text-gray-300 hover:bg-gray-800 hover:text-white"
            >
              <Hash className="h-4 w-4 mr-2" />
              Copy Channel ID
            </DropdownMenuItem>
            {canManage && (
              <>
                <DropdownMenuSeparator className="bg-gray-800" />
                <DropdownMenuItem
                  onClick={() => onBlacklist(connection.serverId)}
                  className="text-orange-300 hover:bg-orange-600/10 hover:text-orange-200"
                >
                  <Ban className="h-4 w-4 mr-2" />
                  Blacklist Server
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onRemove(connection.id, connection.server?.name || 'Unknown Server')}
                  className="text-red-300 hover:bg-red-600/10 hover:text-red-200"
                >
                  <Trash className="h-4 w-4 mr-2" />
                  Remove Connection
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
