---
title: Frequently Asked Questions
description: Quick answers to the most common questions about InterChat. Get up to speed fast with clear, practical information.
icon: HelpCircle
---


# Frequently Asked Questions

Get quick answers to the most common InterChat questions. Can't find what you're looking for? Check our [complete guides](/docs/guides) or join our [support community](https://discord.gg/cgYgC6YZyX).

## Getting Started

<Tabs items={['What is InterChat?', 'How do I start?', 'Is it free?']}>
  <Tab>
    **InterChat connects Discord communities** by enabling real-time cross-server communication.

    **How it works:**
    1. Multiple Discord servers join a "hub" (community topic)
    2. Each server connects one channel to the hub
    3. Messages sent in connected channels appear instantly across all servers
    4. Users keep their original names and avatars

    **Perfect for:**
    - Gaming communities spanning multiple servers
    - Educational networks and study groups
    - Creative collaborations and feedback
    - Professional networking and knowledge sharing

    **Think of it as:** A bridge between Discord servers that preserves each community's independence while enabling broader connections.
  </Tab>
  <Tab>
    **Quick Start (5 minutes):**

    1. **Add InterChat:** Visit [interchat.tech/invite](https://interchat.tech/invite)
    2. **Find a hub:** Visit [interchat.tech/hubs](https://interchat.tech/hubs) to discover communities
    3. **Connect:** Use `/connect hub:HubName` in your chosen channel
    4. **Start chatting:** Send messages and connect with other servers!

    **Need "Manage Server" permission** to add bots and create connections.

    **First time?** Follow our [complete getting started guide](/docs/getting-started) for detailed instructions.
  </Tab>
  <Tab>
    **Yes, completely free!**

    ✅ No message limits or restrictions
    ✅ No ads or monetization
    ✅ Open-source and community-driven

    **How we sustain the project:**
    - Community donations and support ([donate here](https://interchat.tech/donate))
    - Open-source contributions
    - Volunteer moderation and development

    **Want to support us?** Join our [community](https://discord.gg/cgYgC6YZyX) and help other users!
  </Tab>
</Tabs>

## Understanding Hubs

### What exactly is a hub?

A **hub is a themed community** that connects multiple Discord servers around a shared interest or topic. Think of it as a virtual meeting place where different communities come together.

**Examples:**
- **"Indie Game Developers"** - Connects servers of game developers for collaboration
- **"Computer Science Students"** - Links study groups and academic servers
- **"Digital Artists Network"** - Brings together art communities for feedback and inspiration

### How do I choose the right hub?

**Consider these factors:**

<Tabs items={['Topic Match', 'Community Size', 'Activity Level', 'Moderation Quality']}>
  <Tab>
    **Find hubs that match your server's focus:**

    - **Gaming:** Look for specific games or genres
    - **Learning:** Find subject-specific study groups
    - **Creative:** Seek communities in your artistic medium
    - **Professional:** Connect with industry-specific networks

    **Pro tip:** Specific hubs (like "Minecraft Builders") often work better than general ones (like "Gaming Hub").
  </Tab>
  <Tab>
    **Hub size affects experience:**

    - **Small hubs (5-15 servers):** More intimate, easier to build relationships
    - **Medium hubs (15-50 servers):** Good balance of activity and manageability
    - **Large hubs (50+ servers):** High activity but can be overwhelming

    **Sweet spot:** 20-40 connected servers for most communities.
  </Tab>
  <Tab>
    **Check recent activity:**

    - Look for daily conversations
    - Check message timestamps in hub browser
    - Ask current members about typical activity
    - Consider time zones of other servers

    **Red flags:** No recent messages, very sporadic activity, or complaints about inactivity.
  </Tab>
  <Tab>
    **Quality moderation matters:**

    - Clear, reasonable rules
    - Responsive moderators
    - Good community reputation
    - Effective content filtering

    **Check:** Hub ratings, reviews, and how moderators handle issues.
  </Tab>
</Tabs>

### Can I create my own hub?

**Absolutely!** Use `/hub create` and fill out the creation form.

**Before creating, consider:**
- Do you have a clear purpose and target audience?
- Are you prepared to actively moderate and engage?
- Do you have potential servers ready to join?
- Can you commit time to building the community?

**Success tips:**
- Start with a specific, focused topic
- Invite servers you already know first
- Be active in your own hub
- Set clear rules and expectations early

[**Complete hub creation guide →**](/docs/guides/creating-hubs)
## Connections and Channels

### Which channel should I connect?

**Best practices for channel selection:**

<Tabs items={['Create Dedicated', 'Use Existing', 'Avoid These']}>
  <Tab>
    **Recommended: Create a dedicated channel**

    **Benefits:**
    - Clear purpose and expectations
    - No interference with existing server culture
    - Easier to moderate and manage
    - Better member understanding

    **Good names:**
    - `#interchat`
    - `#hub-chat`
    - `#cross-server`
    - `#[hub-name]-chat`

    **Setup tip:** Add a channel description explaining the connection.
  </Tab>
  <Tab>
    **Using existing channels can work if:**

    - Channel topic matches the hub perfectly
    - Community is already engaged and respectful
    - Channel isn't too busy or overwhelming
    - Members are open to cross-server interaction

    **Good candidates:**
    - Topic-specific channels (`#game-dev`, `#art-share`)
    - Active but not overwhelming channels
    - Channels with positive, welcoming communities

    **Preparation:** Announce the connection to your community first.
  </Tab>
  <Tab>
    **Avoid connecting these channels:**

    ❌ **#general** - Usually too busy and unfocused
    ❌ **#announcements** - One-way communication only
    ❌ **Private/restricted channels** - Limits participation
    ❌ **Drama-prone channels** - Will spread problems to hub
    ❌ **Off-topic channels** - Won't match hub purpose

    **Also avoid:**
    - Forum channels (not supported)
    - Voice channels (text only)
    - Channels with frequent bot spam
  </Tab>
</Tabs>

### Why aren't my messages appearing elsewhere?

**Most common causes and quick fixes:**

1. **Permission Issues** (Most common)
   - Check: Does InterChat have "Manage Webhooks" permission?
   - Fix: Server Settings → Roles → InterChat → Enable required permissions

2. **Connection Problems**
   - Check: Run `/connection list` to see status
   - Fix: If paused, use `/connection unpause`

3. **Webhook Issues**
   - Check: Was the webhook manually deleted?
   - Fix: Use `/connection edit` to refresh the webhook

4. **Hub Filters**
   - Check: Are your messages being filtered for content?
   - Fix: Try simple messages like "test" or contact hub moderators

[**Complete troubleshooting guide →**](/docs/guides/troubleshooting)

## Moderation and Safety

### How does hub moderation work?

**Three-tier system for community safety:**

<Tabs items={['Moderator Roles', 'Available Actions', 'Reporting System']}>
  <Tab>
    **Hub Moderator** 🛡️
    - Warn users for rule violations
    - Blacklist problematic users or servers
    - Delete inappropriate messages
    - View infraction history

    **Hub Manager** ⚙️
    - All moderator permissions
    - Configure hub settings and rules
    - Add/remove moderators
    - Manage content filtering

    **Hub Owner** 👑
    - Full control over the hub
    - Add/remove managers
    - Transfer ownership
    - Delete the hub
  </Tab>
  <Tab>
    **Warning System:**
    - Formal warnings for rule violations
    - Tracked in user's infraction history
    - Escalation path for repeat offenders

    **Blacklisting:**
    - **User blacklist:** Removes individual from hub
    - **Server blacklist:** Removes entire server
    - **Temporary or permanent** options available
    - **Appeals process** for disputed actions

    **Content Management:**
    - Delete inappropriate messages
    - Remove reactions from messages
    - Configure automated content filtering
  </Tab>
  <Tab>
    **Easy Reporting:**
    Right-click any message → Apps → InterChat → Report Message

    **What happens:**
    1. Report sent to hub moderators instantly
    2. Moderators review and investigate
    3. Appropriate action taken based on violation
    4. Reporter may receive follow-up if needed

    **Report categories:**
    - Spam or unwanted content
    - Harassment or abuse
    - Inappropriate content
    - Rule violations
    - Other concerns
  </Tab>
</Tabs>

### What if there's drama or conflict?

**Hub moderators handle conflicts, but you have options:**

**Immediate actions:**
- Report problematic content using the report system
- Contact hub moderators directly
- Temporarily pause your connection if needed: `/connection pause`

**Server admin powers:**
- You can disconnect from any hub at any time
- Hub moderation cannot override your server's autonomy
- You maintain full control over your own community

**Prevention tips:**
- Choose well-moderated hubs with clear rules
- Set expectations with your community before connecting
- Monitor cross-server interactions initially
- Build relationships with hub moderators

## Technical Questions

### What permissions does InterChat need?

**Essential permissions:**

<Tabs items={['Required', 'Recommended', 'Why These?']}>
  <Tab>
    **Must have for basic functionality:**

    ✅ **Manage Webhooks** - Creates message delivery system
    ✅ **Send Messages** - Relays messages between servers
    ✅ **Read Messages** - Sees messages to relay
    ✅ **View Channels** - Accesses connected channels

    **Without these, InterChat cannot function properly.**
  </Tab>
  <Tab>
    **Helpful for full feature set:**

    ✅ **Manage Messages** - Moderation features
    ✅ **Embed Links** - Rich message formatting
    ✅ **Add Reactions** - Cross-server reaction sync
    ✅ **Use External Emojis** - Better message display

    **These enhance the experience but aren't strictly required.**
  </Tab>
  <Tab>
    **Why webhooks are essential:**

    Webhooks allow messages to appear with the **original sender's name and avatar**, making cross-server chat feel natural and authentic.

    **Without webhooks:** All messages would appear to come from the InterChat bot, losing the personal connection that makes cross-server communication effective.

    **Security:** Webhooks are Discord's official system for this functionality and are completely safe.
  </Tab>
</Tabs>

### Is my data safe?

**Privacy and security overview:**

**What we process:**
- Messages temporarily to relay between servers
- User IDs for moderation and hub management
- Server information for connection management

**What we DON'T store:**
- Message content long-term
- Private conversations
- Personal information beyond Discord IDs

**Security measures:**
- Open-source code (you can review it)
- Minimal data collection
- No selling or sharing of data
- AGPL-3.0 license ensures transparency

**Self-hosting option:** Run your own instance for complete control.

## Getting Help

### Where can I get support?

**Self-Service Resources:**
- 📖 [Complete Guides](/docs/guides) - Comprehensive tutorials
- 🔧 [Troubleshooting Guide](/docs/guides/troubleshooting) - Fix common issues
- 💬 [Command Reference](/docs/reference/commands) - All commands explained

**Community Support:**
- 🌟 [Support Server](https://discord.gg/cgYgC6YZyX) - Live help from community
- 💡 Ask experienced users and moderators
- 🚀 Get tips and best practices
- 🐛 Report bugs and suggest features

**What to include in support requests:**
1. Clear description of the problem
2. Steps you've already tried
3. Error messages (exact text or screenshots)
4. When the issue started
5. Relevant command outputs

<Callout type="info">
  **Still have questions?** Join our [support community](https://discord.gg/cgYgC6YZyX) where our team and experienced users are ready to help! We're friendly and responsive.
</Callout>
