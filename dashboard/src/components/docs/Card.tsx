import React from 'react';

interface CardProps {
  title?: string;
  href?: string;
  children?: React.ReactNode;
  className?: string;
}

export function Card({ title, href, children, className = "" }: CardProps) {
  const cardContent = (
    <div className={`border rounded-lg p-4 ${className}`}>
      {title && <h3 className="font-semibold mb-2">{title}</h3>}
      {children}
    </div>
  );

  if (href) {
    return (
      <a href={href} className="block hover:opacity-80 transition-opacity">
        {cardContent}
      </a>
    );
  }

  return cardContent;
}

interface CardsProps {
  children: React.ReactNode;
  className?: string;
}

export function Cards({ children, className = "" }: CardsProps) {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 gap-4 my-6 ${className}`}>
      {children}
    </div>
  );
}
