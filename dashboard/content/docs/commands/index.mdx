---
title: Commands Overview
description: Complete reference of InterChat commands
---


# Commands Overview

InterChat offers a comprehensive set of commands to manage hubs, connections, and moderation. All commands are available as slash commands (`/command`) and most are also available as prefix commands (`.command`).

<Callout type="info">
  InterChat uses <PERSON><PERSON>'s slash command system. Type `/` in your Discord chat to see all available commands.
</Callout>

## Command Categories

<Cards>
  <Card title="Hub Commands" href="/docs/commands/hub-commands">
    Create and manage hubs, configure settings, and handle invitations
  </Card>
  <Card title="Connection Commands" href="/docs/commands/connection-commands">
    Connect channels to hubs, manage existing connections
  </Card>
  <Card title="Moderation Commands" href="/docs/commands/moderation-commands">
    Blacklist users/servers, handle reports, and manage infractions
  </Card>
</Cards>

## Command Permissions

InterChat commands have different permission requirements:

| Permission Level | Description |
| --- | --- |
| Everyone | Basic commands that any user can use |
| Server Admin | Commands that require server administrator permissions |
| Hub Moderator | Commands that require hub moderator status |
| Hub Manager | Commands that require hub manager status |
| Hub Owner | Commands that only the hub owner can use |

Each command page specifies the required permissions.

## Command Syntax

InterChat commands follow this general syntax:

```
/command subcommand option1:value1 option2:value2
```

For example:
```
/hub create name:Gaming Hub description:A hub for gaming communities
```

## Command Cooldowns

Some commands have cooldowns to prevent abuse:

| Command | Cooldown |
| --- | --- |
| `/hub create` | 10 minutes |
| `/hub announce` | 1 minute |
| `/connect` | 30 seconds |

Cooldowns are per-user, not per-server.

## Using Prefix Commands

In addition to slash commands, InterChat supports prefix commands using the `.` prefix:

```
i.hub create
i.connect
i.blacklist user
```

Prefix commands will guide you through the options interactively.

<Callout type="info">
  Slash commands are recommended as they provide better guidance and error handling.
</Callout>
