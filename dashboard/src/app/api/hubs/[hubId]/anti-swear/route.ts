import { type NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/lib/prisma";
import { getUserHubPermission } from "@/lib/permissions";
import { PermissionLevel } from "@/lib/constants";
import { z } from "zod/v4";
import { BlockWordAction } from "@/lib/generated/prisma/client";
import { withAuthRateLimit, withStrictRateLimit } from "@/lib/rate-limit-middleware";
import { ENDPOINT_RATE_LIMITS } from "@/lib/rate-limit-config";

// Schema for pattern validation
const patternSchema = z.object({
  pattern: z.string().min(1),
});

// Schema for creating an anti-swear rule
const createAntiSwearRuleSchema = z.object({
  name: z.string().min(3).max(50),
  patterns: z.array(patternSchema).min(1),
  actions: z.array(z.enum(BlockWordAction)).min(1),
});

// Schema for updating an anti-swear rule
const updateAntiSwearRuleSchema = z.object({
  id: z.string(),
  name: z.string().min(3).max(50).optional(),
  patterns: z.array(patternSchema).optional(),
  actions: z.array(z.enum(BlockWordAction)).min(1).optional(),
});

// GET all anti-swear rules for a hub
async function handleGET(
  request: NextRequest,
  props: { params: Promise<{ hubId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { hubId } = await props.params;

    // Check if user has permission to view this hub
    const permissionLevel = await getUserHubPermission(session.user.id, hubId);
    if (permissionLevel < PermissionLevel.MODERATOR) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Get the hub's anti-swear rules with their patterns
    const antiSwearRules = await db.antiSwearRule.findMany({
      where: { hubId },
      include: {
        patterns: true,
      },
      orderBy: { createdAt: "desc" },
    });

    return NextResponse.json(antiSwearRules);
  } catch (error) {
    console.error("Error fetching anti-swear rules:", error);
    return NextResponse.json(
      { error: "Failed to fetch anti-swear rules" },
      { status: 500 }
    );
  }
}

// POST (create) a new anti-swear rule
async function handlePOST(
  request: NextRequest,
  props: { params: Promise<{ hubId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { hubId } = await props.params;

    // Check if user has permission to edit this hub
    const permissionLevel = await getUserHubPermission(session.user.id, hubId);
    if (permissionLevel < PermissionLevel.MANAGER) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validation = createAntiSwearRuleSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validation.error.message },
        { status: 400 }
      );
    }

    const { name, patterns, actions } = validation.data;

    // Check if a rule with this name already exists for this hub
    const existingRule = await db.antiSwearRule.findUnique({
      where: {
        hubId_name: {
          hubId,
          name,
        },
      },
    });

    if (existingRule) {
      return NextResponse.json(
        { error: "A rule with this name already exists" },
        { status: 400 }
      );
    }

    // Create the anti-swear rule with its patterns
    const rule = await db.antiSwearRule.create({
      data: {
        hubId,
        name,
        actions,
        createdBy: session.user.id,
        patterns: {
          create: patterns.map(p => ({
            pattern: p.pattern,
          })),
        },
      },
      include: {
        patterns: true,
      },
    });

    return NextResponse.json(rule);
  } catch (error) {
    console.error("Error creating anti-swear rule:", error);
    return NextResponse.json(
      { error: "Failed to create anti-swear rule" },
      { status: 500 }
    );
  }
}

// DELETE an anti-swear rule
async function handleDELETE(
  request: NextRequest,
  props: { params: Promise<{ hubId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { hubId } = await props.params;

    // Check if user has permission to edit this hub
    const permissionLevel = await getUserHubPermission(session.user.id, hubId);
    if (permissionLevel < PermissionLevel.MANAGER) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const searchParams = new URL(request.url).searchParams;
    const ruleId = searchParams.get("id");

    if (!ruleId) {
      return NextResponse.json(
        { error: "Rule ID is required" },
        { status: 400 }
      );
    }

    // Check if the rule exists and belongs to this hub
    const rule = await db.antiSwearRule.findUnique({
      where: { id: ruleId },
      select: { hubId: true },
    });

    if (!rule) {
      return NextResponse.json(
        { error: "Anti-swear rule not found" },
        { status: 404 }
      );
    }

    if (rule.hubId !== hubId) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Delete the rule (patterns will be automatically deleted due to cascade)
    await db.antiSwearRule.delete({
      where: { id: ruleId },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting anti-swear rule:", error);
    return NextResponse.json(
      { error: "Failed to delete anti-swear rule" },
      { status: 500 }
    );
  }
}

// PATCH (update) an anti-swear rule
async function handlePATCH(
  request: NextRequest,
  props: { params: Promise<{ hubId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { hubId } = await props.params;

    // Check if user has permission to edit this hub
    const permissionLevel = await getUserHubPermission(session.user.id, hubId);
    if (permissionLevel < PermissionLevel.MANAGER) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validation = updateAntiSwearRuleSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validation.error.message },
        { status: 400 }
      );
    }

    const { id, name, patterns, actions } = validation.data;

    // Check if the rule exists and belongs to this hub
    const rule = await db.antiSwearRule.findUnique({
      where: { id },
      include: {
        patterns: true,
      },
    });

    if (!rule) {
      return NextResponse.json(
        { error: "Anti-swear rule not found" },
        { status: 404 }
      );
    }

    if (rule.hubId !== hubId) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Start a transaction to update the rule and its patterns
    const updatedRule = await db.$transaction(async (tx) => {
      // Update the rule itself
      const updated = await tx.antiSwearRule.update({
        where: { id },
        data: {
          ...(name && { name }),
          ...(actions && { actions }),
          updatedAt: new Date(),
        },
        include: {
          patterns: true,
        },
      });

      // If patterns are provided, update them
      if (patterns) {
        // Delete all existing patterns
        await tx.antiSwearPattern.deleteMany({
          where: { ruleId: id },
        });

        // Create new patterns
        await tx.antiSwearPattern.createMany({
          data: patterns.map(p => ({
            ruleId: id,
            pattern: p.pattern,
          })),
        });

        // Fetch the updated rule with new patterns
        return await tx.antiSwearRule.findUnique({
          where: { id },
          include: {
            patterns: true,
          },
        });
      }

      return updated;
    });

    return NextResponse.json(updatedRule);
  } catch (error) {
    console.error("Error updating anti-swear rule:", error);
    return NextResponse.json(
      { error: "Failed to update anti-swear rule" },
      { status: 500 }
    );
  }
}

// Apply rate limiting to the handlers
export const GET = withAuthRateLimit(handleGET, {
  tier: ENDPOINT_RATE_LIMITS.MODERATION.GET_ANTI_SWEAR,
});

export const POST = withStrictRateLimit(handlePOST, {
  tier: ENDPOINT_RATE_LIMITS.MODERATION.CREATE_ANTI_SWEAR,
  customMessage: "Anti-swear rule creation rate limit exceeded. Please wait before creating another rule.",
});

export const DELETE = withStrictRateLimit(handleDELETE, {
  tier: ENDPOINT_RATE_LIMITS.MODERATION.DELETE_ANTI_SWEAR,
  customMessage: "Anti-swear rule deletion rate limit exceeded. Please wait before deleting another rule.",
});

export const PATCH = withStrictRateLimit(handlePATCH, {
  tier: ENDPOINT_RATE_LIMITS.MODERATION.UPDATE_ANTI_SWEAR,
  customMessage: "Anti-swear rule update rate limit exceeded. Please wait before updating another rule.",
});
