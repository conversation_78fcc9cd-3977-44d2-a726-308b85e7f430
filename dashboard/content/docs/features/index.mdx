---
title: Features Overview
description: Explore the powerful features of InterChat
---


# Features Overview

InterChat offers a comprehensive set of features designed to connect Discord communities and provide powerful moderation tools.

<Cards>
  <Card title="Hubs" href="/docs/features/hubs">
    Create and manage community hubs to connect servers with shared interests
  </Card>
  <Card title="Connections" href="/docs/features/connections">
    Connect channels across different Discord servers for real-time messaging
  </Card>
  <Card title="Moderation" href="/docs/features/moderation">
    Comprehensive tools to keep your communities safe
  </Card>
  <Card title="Blacklisting" href="/docs/features/blacklisting">
    Ban problematic users or servers from your hubs
  </Card>
  <Card title="Reporting" href="/docs/features/reporting">
    Report inappropriate content and users
  </Card>
  <Card title="Announcements" href="/docs/features/announcements">
    Send important messages to all channels in your hub
  </Card>
  <Card title="Welcome Messages" href="/docs/features/welcome-messages">
    Customize greetings for new servers joining your hub
  </Card>
  <Card title="Reactions" href="/docs/features/reactions">
    Use emoji reactions across servers in your hub
  </Card>
  <Card title="Inbox" href="/docs/features/inbox">
    Manage notifications and important messages
  </Card>
</Cards>

## Core Functionality

InterChat's primary purpose is to enable cross-server communication through a system of hubs and connections:

1. **Hubs** are central meeting points that multiple servers can connect to
2. **Connections** link specific channels in your server to a hub
3. **Messages** sent in connected channels are relayed to all other connected channels in the hub

This creates a real-time communication experience across different Discord communities, allowing for collaboration, networking, and community building beyond the boundaries of individual servers.

## Advanced Features

Beyond basic cross-server chat, InterChat offers:

- **Custom Hub Rules**: Set specific rules for your hub that all participants must follow
- **Content Filtering**: Automatically filter inappropriate content using customizable filters
- **Moderation Tools**: Comprehensive tools for moderators to manage the hub
- **Logging**: Detailed logs of all activities in your hub
- **Webhooks**: real-time message delivery using Discord's webhook system

Explore each feature in detail using the navigation menu.
