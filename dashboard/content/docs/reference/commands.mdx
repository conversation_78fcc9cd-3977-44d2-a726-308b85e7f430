---
title: Complete Command Reference
description: Comprehensive reference for all InterChat commands with syntax, examples, and permission requirements.
icon: Terminal
---


# Complete Command Reference

This is the definitive reference for all InterChat commands. Commands are organized by category with complete syntax, examples, and permission requirements.

<Callout type="info">
  **New to commands?** Start with our [Essential Commands Guide](/docs/guides/commands) for the most commonly used commands with practical examples.
</Callout>

## Command Syntax

InterChat supports both slash commands and prefix commands:

**Slash Commands (Recommended):**
```
/command option1:value1 option2:value2
```

**Prefix Commands:**
```
i.command
```

## Permission Levels

| Level | Description | Commands Available |
|-------|-------------|-------------------|
| **Everyone** | Any Discord user | Basic info, browsing, reporting |
| **Server Admin** | Manage Server permission | Connection management, hub creation |
| **Hub Moderator** | Hub moderation role | Warnings, blacklists, infractions |
| **Hub Manager** | Hub management role | Most hub settings, moderator management |
| **Hub Owner** | Hub creator/owner | All hub commands, ownership transfer |
| **Staff** | InterChat Staff | Global moderation, debug tools |
| **Developer** | InterChat Developers | Advanced debug tools |

## Hub Commands

### Hub Creation & Management

#### `/hub create`
**Purpose:** Create a new InterChat hub
**Permissions:** Everyone
**Cooldown:** 10 minutes

**Usage:**
```
/hub create
```

**Process:**
1. Opens a modal form
2. Fill in hub name, description, and optional icon URL
3. Submit to create your hub
4. You become the hub owner

**Example Form Data:**
- **Name:** "Indie Game Developers"
- **Description:** "A community for independent game developers to share resources, get feedback, and collaborate on projects."
- **Icon URL:** "https://example.com/icon.png" (optional)

---

#### `/hub edit`
**Purpose:** Edit hub details (name, description, icon, locked/NSFW status)
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub edit hub:HubName
```

**Options:**
- `hub` - The name of the hub to edit

**Process:**
1. Displays an interactive menu with the hub's current settings.
2. Provides direct buttons to toggle `Locked` and `NSFW` status.
3. Offers a select menu to edit `Description`, `Icon URL`, and `Banner URL` via modals.

---

#### `/hub delete`
**Purpose:** Permanently delete a hub
**Permissions:** Hub Owner

**Usage:**
```
/hub delete hub:HubName
```

<Callout type="warning">
  **Warning:** This action is permanent and cannot be undone. All connections will be removed.
</Callout>

---

#### `/hub transfer`
**Purpose:** Transfer hub ownership to another user
**Permissions:** Hub Owner

**Usage:**
```
/hub transfer hub:HubName user:@NewOwner
```

**Options:**
- `hub` - The name of the hub
- `user` - The user to transfer ownership to

---

#### `/hub visibility`
**Purpose:** Change hub visibility (public/private)
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub visibility hub:HubName visibility:public
/hub visibility hub:HubName visibility:private
```

**Requirements for Public Hubs:**
- Hub must be 24+ hours old
- Must have 2+ moderators
- Must have report logging configured

---

#### `/hub servers`
**Purpose:** List all servers connected to a hub, or get details about a specific server's connection.
**Permissions:** Hub Owner, Hub Manager, Hub Moderator

**Usage:**
```
/hub servers hub:HubName
/hub servers hub:HubName server:ServerID
```

**Options:**
- `hub` - The name of the hub
- `server` (Optional) - The ID of a specific server to view details for.

Shows server names, connection times, and activity levels.

---

#### `/hub rename`
**Purpose:** Renames a hub. (Premium Feature)
**Permissions:** Hub Owner

**Usage:**
```
/hub rename hub:OldHubName name:NewHubName
```

**Options:**
- `hub` - The current name of the hub to rename.
- `name` - The new desired name for the hub (1-50 characters).

---

### Hub Discovery & Joining

#### **Hub Directory Website**
**Purpose:** Browse and discover public hubs
**Access:** Everyone

**How to Access:**
Visit [interchat.tech/hubs](https://interchat.tech/hubs) directly in your browser.

**Features:**
- Browse public hubs by category
- Search for specific topics and interests
- View ratings, reviews, and member counts
- Filter by activity level and community size
- Join hubs directly from the website

---

#### `/hub join`
**Purpose:** Join a hub using an invitation code
**Permissions:** Everyone

**Usage:**
```
/hub join invite:abc123def
```

**Options:**
- `invite` - The invitation code provided by the hub owner

### Hub Configuration

#### `/hub config rules`
**Purpose:** Set or update hub rules
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub config rules hub:HubName
```

Opens a modal where you can enter hub rules that will be shown to users when they join.

---

#### `/hub config welcome`
**Purpose:** Set welcome message for new servers
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub config welcome hub:HubName
```

**Available Variables:**
- `{hubName}` - Hub's name
- `{serverName}` - Joining server's name
- `{user}` - User who connected the channel
- `{memberCount}` - Server member count
- `{totalConnections}` - Total connected servers

---

#### `/hub config settings`
**Purpose:** Configure general hub settings
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub config settings hub:HubName
```

**Available Settings:**
- **Block Invites** - Prevents Discord invite links
- **Use Nicknames** - Shows server nicknames instead of usernames
- **Block NSFW** - Filters inappropriate images
- **Hide Links** - Prevents links from being sent
- **Spam Filter** - Enables spam detection
- **Reactions** - Allows emoji reactions across servers

---

#### `/hub config logging`
**Purpose:** Configure activity logging channels
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub config logging hub:HubName
```

**Log Types:**
- **Mod Logs** (`modLogs`) - Record moderation actions
- **Join/Leaves** (`joinLeaves`) - Track server connections
- **Reports** (`reports`) - User-submitted content reports
- **Appeals** (`appeals`) - Blacklist appeal requests
- **Network Alerts** (`networkAlerts`) - System notifications

---

#### `/hub config anti-swear`
**Purpose:** Configure content filtering rules
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub config anti-swear hub:HubName
```

**Pattern Types:**
- `word` - Exact word match
- `word*` - Prefix matching
- `*word` - Suffix matching
- `*word*` - Contains matching

**Actions:**
- **Block** - Prevent message from being sent
- **Blacklist** - Automatically blacklist sender (10 minutes)
- **Send Alert** - Notify moderators

---

#### `/hub config set-appeal-cooldown`
**Purpose:** Sets the cooldown period for blacklist appeals.
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub config set-appeal-cooldown hub:HubName cooldown:duration
```

**Options:**
- `hub` - The name of the hub
- `cooldown` - Duration (e.g., "7d" for 7 days)

### Hub Moderation

#### `/hub moderator add`
**Purpose:** Add a moderator to the hub
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub moderator add hub:HubName user:@User position:moderator
/hub moderator add hub:HubName user:@User position:manager
```

**Positions:**
- `moderator` - Basic moderation permissions
- `manager` - Advanced management permissions

---

#### `/hub moderator remove`
**Purpose:** Remove a moderator from the hub
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub moderator remove hub:HubName user:@User
```

---

#### `/hub moderator edit`
**Purpose:** Changes a moderator's position.
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub moderator edit hub:HubName user:@User position:moderator|manager
```

**Options:**
- `hub` - The name of the hub
- `user` - The moderator to edit
- `position` - New position: "moderator" or "manager"

---

#### `/hub moderator list`
**Purpose:** List all hub moderators
**Permissions:** Hub Owner, Hub Manager, Hub Moderator

**Usage:**
```
/hub moderator list hub:HubName
```

Shows all moderators with their roles and permissions.

### Hub Communication

#### `/hub announce`
**Purpose:** Send announcement to all connected channels
**Permissions:** Hub Owner, Hub Manager, Hub Moderator
**Cooldown:** 1 minute

**Usage:**
```
/hub announce hub:HubName
```

Opens a modal for entering your announcement (up to 4000 characters).

### Hub Invitations

#### `/hub invite create`
**Purpose:** Create invitation codes for private hubs
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub invite create hub:HubName expiry:7d
/hub invite create hub:HubName uses:10 expiry:24h
```

**Options:**
- `expiry` - How long the invite is valid (1h, 24h, 7d, 30d)
- `uses` - Maximum number of uses (optional)

---

#### `/hub invite list`
**Purpose:** List all active invitations
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub invite list hub:HubName
```

---

#### `/hub invite revoke`
**Purpose:** Revoke an existing invitation
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub invite revoke hub:HubName code:abc123def
```

### Hub Information

#### `/hub infractions`
**Purpose:** View infractions in a hub
**Permissions:** Hub Owner, Hub Manager, Hub Moderator

**Usage:**
```
/hub infractions hub:HubName
/hub infractions hub:HubName user:123456789012345678
```

**Options:**
- `user` - View infractions for specific user (optional)

Shows blacklists, warnings, and their status.

## Connection Commands

### Basic Connection Management

#### `/connect`
**Purpose:** Connects your channel to an InterChat hub.
**Permissions:** Server Admin
**Cooldown:** 30 seconds

**Usage:**
```
/connect [channel:<channel>] [hub:<hub_name>] [invite:<invite_code>]
```

**Options:**
- `channel` (Optional) - The channel to connect. Defaults to the current channel.
- `hub` (Optional) - The name of the public hub to connect to. Autocomplete is available.
- `invite` (Optional) - The invite code for a private hub.

**Process:**
1. If no hub or invite is provided, an interactive UI is displayed to help you choose a hub or create a new one.
2. If a hub name or invite is provided, the bot attempts to join the specified hub.
3. Creates a webhook in the channel (if one doesn't exist or is invalid).
4. Establishes a connection to the specified hub.
5. Sends a confirmation message.

---

#### `/disconnect`
**Purpose:** Disconnect current channel from its hub
**Permissions:** Server Admin

**Usage:**
```
/disconnect
```

<Callout type="warning">
  **Warning:** This action is permanent. You'll need to create a new connection to rejoin the hub.
</Callout>

### Connection Monitoring

#### `/connection list`
**Purpose:** List all connections in the server
**Permissions:** Server Admin

**Usage:**
```
/connection list
```

Shows:
- Connected channels and their hubs
- Connection status (active/paused)
- Last activity timestamps

---

#### `/connection pause`
**Purpose:** Temporarily pause a connection
**Permissions:** Server Admin

**Usage:**
```
/connection pause channel:#channel
/connection pause
```

**Options:**
- `channel` - Specific channel to pause (defaults to current channel)

---

#### `/connection unpause`
**Purpose:** Resume a paused connection
**Permissions:** Server Admin

**Usage:**
```
/connection unpause channel:#channel
/connection unpause
```

---

#### `/connection edit`
**Purpose:** Modify connection settings
**Permissions:** Server Admin

**Usage:**
```
/connection edit channel:#channel
/connection edit
```

**Available Options:**
- Change connected channel
- Toggle compact mode
- Update webhook settings

## Moderation Commands

### User Moderation

#### `/warn`
**Purpose:** Issue a formal warning to a user
**Permissions:** Hub Owner, Hub Manager, Hub Moderator

**Usage:**
```
/warn user:@Username hub:HubName reason:"Please keep discussions on-topic"
```

**Options:**
- `user` - The user to warn
- `hub` - The hub where the warning applies
- `reason` - Explanation for the warning

---

#### `/blacklist user`
**Purpose:** Blacklist a user from a hub
**Permissions:** Hub Owner, Hub Manager, Hub Moderator

**Usage:**
```
/blacklist user hub:HubName user:@User reason:"Repeated spam" duration:7d
/blacklist user hub:HubName user:123456789012345678 reason:"Harassment"
```

**Options:**
- `hub` - The hub name
- `user` - User ID or mention
- `reason` - Reason for blacklisting
- `duration` - Optional duration (1h, 1d, 7d, 30d, etc.)

---

#### `/blacklist server`
**Purpose:** Blacklist an entire server from a hub
**Permissions:** Hub Owner, Hub Manager, Hub Moderator

**Usage:**
```
/blacklist server hub:HubName server:123456789012345678 reason:"Multiple violations"
```

**Options:**
- `hub` - The hub name
- `server` - Server ID
- `reason` - Reason for blacklisting
- `duration` - Optional duration

---

#### `/unblacklist user`
**Purpose:** Remove user from blacklist
**Permissions:** Hub Owner, Hub Manager, Hub Moderator

**Usage:**
```
/unblacklist user hub:HubName user:@User
```

---

#### `/unblacklist server`
**Purpose:** Remove server from blacklist
**Permissions:** Hub Owner, Hub Manager, Hub Moderator

**Usage:**
```
/unblacklist server hub:HubName server:123456789012345678
```

### Blacklist Management

#### `/blacklist list`
**Purpose:** View all active blacklists in a hub
**Permissions:** Hub Owner, Hub Manager, Hub Moderator

**Usage:**
```
/blacklist list hub:HubName
```

Shows all blacklisted users and servers with reasons and expiry times.

### Message Management

#### `/deletemsg`
**Purpose:** Deletes a message sent using InterChat.
**Permissions:** Message Author, Hub Owner, Hub Manager, Hub Moderator

**Usage:**
```
/deletemsg message:<message_id_or_link>
```

**Options:**
- `message` - The ID or link of the message to delete.

---

#### `/editmsg`
**Purpose:** Edits a message sent using InterChat.
**Permissions:** Message Author, Hub Owner, Hub Manager, Hub Moderator

**Usage:**
```
/editmsg message:<message_id_or_link>
```

**Options:**
- `message` - The ID or link of the message to edit.

---

#### `/modpanel`
**Purpose:** Opens a moderation actions panel for a message.
**Permissions:** Staff, Hub Owner, Hub Manager, Hub Moderator

**Usage:**
```
/modpanel message:<message_id_or_link>
```

**Options:**
- `message` - The ID or link of the message to moderate.

**Process:**
1. Displays an interactive moderation panel with options such as deleting the message, blacklisting the user/server, warning the user, viewing user infractions, and removing all reactions.

## General Commands

#### `/about`
**Purpose:** Provides information about InterChat, its features, and ways to support its development.
**Permissions:** Everyone

**Usage:**
```
/about
```

---

#### `/help`
**Purpose:** Show all available commands
**Permissions:** Everyone

**Usage:**
```
/help
```

Displays a list of all commands available to your permission level.

---

#### `/invite`
**Purpose:** Provides links to invite the InterChat bot to a Discord server and to join its support server.
**Permissions:** Everyone

**Usage:**
```
/invite
```

---

#### `/profile`
**Purpose:** Displays a user's InterChat profile, including their achievements, donation tier, and other relevant information.
**Permissions:** Everyone

**Usage:**
```
/profile [user:<user>]
```

**Options:**
- `user` (Optional) - The user whose profile to view. Defaults to the command invoker.

---

#### `/rules`
**Purpose:** Displays the network rules for InterChat or the specific rules for a given hub.
**Permissions:** Everyone

**Usage:**
```
/rules [hub:<hub_name>]
```

**Options:**
- `hub` (Optional) - The name of a specific hub to view its rules. If not provided, it displays the general InterChat network rules.

---

#### `/stats`
**Purpose:** Show bot statistics
**Permissions:** Everyone

**Usage:**
```
/stats
```

Displays information about bot uptime, connected servers, and active hubs.

---

#### `/support`
**Purpose:** Provides interactive support and troubleshooting guides for common InterChat issues.
**Permissions:** Everyone

**Usage:**
```
/support [category:<category_name>]
```

**Options:**
- `category` (Optional) - A specific category of support to view (e.g., `connection`, `hub`, `permissions`, `premium`, `other`).

---

#### `/tutorial`
**Purpose:** Directs users to the InterChat online guide/wiki for tutorials on how to use the bot.
**Permissions:** Everyone

**Usage:**
```
/tutorial
```

---

#### `/vote`
**Purpose:** Provides information about voting for InterChat on Top.gg and the perks associated with voting.
**Permissions:** Everyone

**Usage:**
```
/vote
```

---

#### `/donors`
**Purpose:** Displays a leaderboard of top donors who support InterChat development and provides overall donation statistics.
**Permissions:** Everyone

**Usage:**
```
/donors
```

---

#### `/messageinfo`
**Purpose:** Retrieves detailed information about a specific message sent through InterChat.
**Permissions:** Everyone

**Usage:**
```
/messageinfo message:<message_id_or_link>
```

**Options:**
- `message` - The ID or link of the message to get information about.

---

#### `/inbox`
**Purpose:** Allows users to check their inbox for important updates and announcements from InterChat.
**Permissions:** Everyone

**Usage:**
```
/inbox
```

---

#### `/setup`
**Purpose:** Guides server administrators through the initial setup process of InterChat in their server.
**Permissions:** Server Admin

**Usage:**
```
/setup
```

---

#### `/call`
**Purpose:** Start a random text-chat with another server
**Permissions:** Everyone
**Cooldown:** 5 minutes

**Usage:**
```
/call
```

**What it does:**
- Connects you with a random server for chat
- Creates a temporary connection between both servers
- Allows cross-server communication
- Includes rating and reporting features

**Note:** Calls are in **beta**. For more active & reliable communication, consider using hubs.

---

#### `/hangup`
**Purpose:** Ends the current one-to-one call.
**Permissions:** Everyone

**Usage:**
```
/hangup
```

---

#### `/skip`
**Purpose:** Skips the current one-to-one call and immediately attempts to find a new match.
**Permissions:** Everyone

**Usage:**
```
/skip
```

---

#### `/leaderboard`
**Purpose:** View various community leaderboards
**Permissions:** Everyone

**Usage:**
```
/leaderboard messages
/leaderboard calls
/leaderboard votes
/leaderboard achievements
```

**Leaderboard Types:**
- **Messages** - Top users and servers by message count
- **Calls** - Most active call participants
- **Votes** - Top voters for InterChat on bot lists
- **Achievements** - Users with the most achievements unlocked

---

#### `/achievements`
**Purpose:** View your or another user's achievements
**Permissions:** Everyone

**Usage:**
```
/achievements
/achievements user:@SomeUser
/achievements view:unlocked
/achievements view:locked
```

**Options:**
- `user` - View another user's achievements (optional)
- `view` - Filter by achievement status (all/unlocked/locked)

**Achievement Categories:**
- Communication milestones
- Community participation
- Special events and activities
- Hidden achievements for discovery

## User Settings Commands

#### `/badges`
**Purpose:** Allows users to configure whether their badges are displayed in messages.
**Permissions:** Everyone

**Usage:**
```
/badges show:<true|false>
```

**Options:**
- `show` - Whether to show or hide your badges in messages (`true` or `false`).

---

#### `/set language`
**Purpose:** Allows users to set the language in which the bot should respond to them.
**Permissions:** Everyone

**Usage:**
```
/set language lang:<language_code>
```

**Options:**
- `lang` - The desired language code (e.g., `en`, `hi`, `es`).

---

#### `/set reply_mentions`
**Purpose:** Allows users to enable or disable being pinged (mentioned) when someone replies to their messages.
**Permissions:** Everyone

**Usage:**
```
/set reply_mentions enable:<true|false>
```

**Options:**
- `enable` - A boolean value (`true` or `false`) to enable or disable reply mentions.

## Staff Commands

#### `/ban`
**Purpose:** Bans users or servers from InterChat globally.
**Permissions:** Staff

**Usage:**
```
/ban duration:<duration> reason:<reason> [user:<user>] [server_id:<server_id>]
```

**Options:**
- `duration` - The duration of the ban (e.g., `1h`, `1d`, `1w`, `30d`, `permanent`).
- `reason` - The reason for the ban (up to 500 characters).
- `user` (Optional) - The user to ban (mention or ID).
- `server_id` (Optional) - The ID of the server to ban.

---

#### `/bans`
**Purpose:** Views and manages all active global bans (user and server) with filtering and search options.
**Permissions:** Staff

**Usage:**
```
/bans [filter:<type>] [search:<query>]
```

**Options:**
- `filter` (Optional) - Filters bans by type: `user`, `server`, `permanent`, `temporary`, or `all`. Defaults to `all`.
- `search` (Optional) - Searches bans by username, server name, reason, or moderator.

---

#### `/unban`
**Purpose:** Unbans a user or server from InterChat globally.
**Permissions:** Staff

**Usage:**
```
/unban target:<target_id_or_search>
```

**Options:**
- `target` - The ID of the ban, or a search query (username, server name, ban ID) to find the ban.

---

#### `/view_reported_call`
**Purpose:** Allows staff members to view details of a reported call and take moderation actions (ban users/servers).
**Permissions:** Staff

**Usage:**
```
/view_reported_call call_id:<call_id>
```

**Options:**
- `call_id` - The ID of the reported call.

---

#### `/debug fix-server`
**Purpose:** Attempts to fix invalid or missing server data in the database.
**Permissions:** Staff

**Usage:**
```
/debug fix-server
```

---

#### `/dev send-alert`
**Purpose:** Sends an alert/announcement to all users, which will appear in their `/inbox`.
**Permissions:** Developer

**Usage:**
```
/dev send-alert
```

---

#### `/find server`
**Purpose:** Retrieves information about a Discord server that InterChat has access to.
**Permissions:** Staff

**Usage:**
```
/find server server:<server_name_or_id> [hidden:<true|false>]
```

**Options:**
- `server` - The name or ID of the server to find.
- `hidden` (Optional) - Whether the response should be ephemeral (hidden from others). Defaults to `true`.

---

#### `/find user`
**Purpose:** Retrieves information about a Discord user.
**Permissions:** Staff

**Usage:**
```
/find user user:<user_id_or_username> [hidden:<true|false>]
```

**Options:**
- `user` - The ID or username of the user to find.
- `hidden` (Optional) - Whether the response should be ephemeral (hidden from others). Defaults to `true`.

---

#### `/leave`
**Purpose:** Makes the bot leave a specified server.
**Permissions:** Developer

**Usage:**
```
/leave server_id:<server_id>
```

**Options:**
- `server_id` - The ID of the server the bot should leave.

---

#### `/recluster`
**Purpose:** Reboots the bot's clusters.
**Permissions:** Developer

**Usage:**
```
/recluster
```

## Context Menu Commands

### Message Actions

**Report Message** (Everyone)
- Right-click message → Apps → InterChat → Report Message
- Select reason for report
- Sends report to hub moderators

**Mod Panel** (Moderators)
- Right-click message → Apps → InterChat → Mod Panel
- Quick access to moderation actions:
  - Delete message
  - Warn user
  - Blacklist user
  - Blacklist server
  - View user infractions
  - Remove all reactions

## Command Cooldowns

| Command | Cooldown | Scope |
|---------|----------|-------|
| `/hub create` | 10 minutes | Per user |
| `/hub announce` | 1 minute | Per user |
| `/connect` | 30 seconds | Per user |
| `/warn` | 5 seconds | Per user |
| `/blacklist` | 10 seconds | Per user |

## Error Handling

**Common Error Messages:**

- **"Missing Permissions"** - You don't have the required role or Discord permissions
- **"Hub Not Found"** - The hub name doesn't exist or is misspelled
- **"Already Connected"** - Channel is already connected to a hub
- **"Cooldown Active"** - You must wait before using this command again
- **"Invalid Duration"** - Duration format is incorrect (use 1h, 1d, 7d, etc.)

**Troubleshooting Tips:**
1. Check your spelling of hub names
2. Verify you have the correct permissions
3. Ensure the bot has necessary Discord permissions
4. Wait for cooldowns to expire
5. Use `/help` to verify command syntax

---

**Need more help?** Check our [Essential Commands Guide](/docs/guides/commands) for practical examples and use cases!