// InterChat v5-focused Homepage
import { CreditsSection } from "@/components/CreditsSection";
import { FaqSection } from "@/components/FaqSection";
import { OpenSourceSection } from "@/components/OpenSourceSection";
import { HomePageSchemas } from "@/components/HomePageSchemas";
import type { Metadata } from "next";
import { V5Hero } from "@/components/v5/V5Hero";
import { V5FeatureShowcase } from "@/components/v5/V5FeatureShowcase";
import { V5ShowcaseStrip } from "@/components/v5/V5ShowcaseStrip";
import { V5CTA } from "@/components/v5/V5CTA";
import { StatsBar } from "@/components/hubs/StatsBar";
import { getPlatformStats, getStatsBarData } from "@/lib/platform-stats";

export const revalidate = 300;

export const metadata: Metadata = {
  title: "InterChat v5 – Faster, modern, and redesigned",
  description:
    "Introducing InterChat v5: a complete rewrite with improved performance, modern command UIs, and a redesigned dashboard for cross-server communication.",
  keywords: [
    "Discord bot",
    "cross-server chat",
    "InterChat v5",
    "Discord hubs",
    "server bridge",
    "Discord moderation",
  ],
  openGraph: {
    title: "InterChat v5 – Faster, modern, and redesigned",
    description:
      "A complete rewrite with improved performance, enhanced command UIs, and a modern dashboard.",
    url: "https://interchat.tech",
    siteName: "InterChat",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "InterChat v5 – Faster, modern, and redesigned",
    description:
      "A complete rewrite with improved performance, enhanced command UIs, and a modern dashboard.",
  },
};

export default async function HomePage() {
  const platformStats = await getPlatformStats();
  const statsData = platformStats.success ? getStatsBarData(platformStats.data) : undefined;

  return (
    <>
      {/* Schema.org markup for SEO */}
      <HomePageSchemas />

      <main
        className="flex flex-1 flex-col justify-center"
        itemScope
        itemType="https://schema.org/WebPage"
      >
        {/* v5 Launch Hero */}
        <V5Hero />

        {/* Live platform stats */}
        <StatsBar stats={statsData} />

        {/* Capabilities & features */}
        <V5ShowcaseStrip />
        <V5FeatureShowcase />

        {/* Community & docs */}
        <OpenSourceSection />
        <CreditsSection />
        <FaqSection />
        <V5CTA />
      </main>
    </>
  );
}
